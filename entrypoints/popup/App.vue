<script lang="ts" setup>
/// <reference types="chrome"/>

import { ref, computed, onMounted } from 'vue'

// 类型定义
interface Project {
  id: string
  name: string
  description: string
  environment: string
  template: string
}

interface Confetto {
  randomModifier: number
  color: { front: string, back: string }
  dimensions: { x: number, y: number }
  position: { x: number, y: number }
  rotation: number
  scale: { x: number, y: number }
  velocity: { x: number, y: number }
}

interface Sequin {
  color: string
  radius: number
  position: { x: number, y: number }
  velocity: { x: number, y: number }
}

interface PageStatus {
  isValid: boolean;
  needsRefresh: boolean;
  hasFormFields: boolean;
  pageType?: string;
  error?: string;
}

interface FormFieldsResponse {
  hasFields: boolean;
  fieldCount: number;
}

// 存储键常量
const STORAGE_KEYS = {
  LAST_MODE: 'formify_last_mode',
  PROJECTS: 'formify_projects',
  SKIP_LOGIN: 'formify_skip_login',
  LAST_LANGUAGE: 'formify_last_language'
} as const

// 状态管理
const showLoginPrompt = ref(true)
const isLoggedIn = ref(false)
const currentMode = ref<'general' | 'email' | 'bugReport'>('general')
const description = ref('')
const selectedProject = ref('')
const selectedLanguage = ref('auto')
const projects = ref<Project[]>([])
const isLoading = ref(false)
const isSuccess = ref(false)
const statusMessage = ref('')
const statusType = ref<'info' | 'error' | 'success'>('info')
const showStatus = ref(false)
const confettiCanvas = ref<HTMLCanvasElement | null>(null)
const userAvatarUrl = ref('path/to/default-avatar.png')
const hasFormFields = ref(false)

// confetti 动画配置
const confettiConfig = {
  confettiCount: 20,
  sequinCount: 10,
  gravityConfetti: 0.3,
  gravitySequins: 0.55,
  dragConfetti: 0.075,
  dragSequins: 0.02,
  terminalVelocity: 3,
  colors: [
    { front: '#7b5cff', back: '#6245e0' }, // Purple
    { front: '#b3c7ff', back: '#8fa5e5' }, // Light blue
    { front: '#5c86ff', back: '#345dd1' }  // Dark blue
  ]
}

// 计算属性
const showProjectSelector = computed(() => currentMode.value === 'bugReport')

const textareaPlaceholder = computed(() => {
  switch (currentMode.value) {
    case 'bugReport': return 'Enter bug description'
    case 'email': return 'Enter email content description'
    default: return 'Enter description'
  }
})

const buttonText = computed(() => {
  if (isSuccess.value) return 'Finish'
  if (isLoading.value) return 'Generating...'
  return 'Generate'
})

const isButtonDisabled = computed(() => {
  return isLoading.value || isSuccess.value || !description.value.trim() || !hasFormFields.value
})

const isInputDisabled = computed(() => {
  return isLoading.value || isSuccess.value || !hasFormFields.value
})

const selectedLanguageText = computed(() => {
  const languageMap: Record<string, string> = {
    'auto': 'Auto',
    'id': 'Bahasa Indonesia',
    'ms': 'Bahasa Melayu',
    'da': 'Dansk',
    'de': 'Deutsch',
    'en': 'English',
    'es': 'Español',
    'fr': 'Français',
    'it': 'Italiano',
    'nl': 'Nederlands',
    'no': 'Norsk',
    'pl': 'Polski',
    'pt': 'Português',
    'ro': 'Română',
    'fi': 'Suomi',
    'sv': 'Svenska',
    'vi': 'Tiếng Việt',
    'tr': 'Türkçe',
    'hu': 'Magyar',
    'cs': 'Čeština',
    'uk': 'Українська',
    'ru': 'Русский',
    'bg': 'Български',
    'ar': 'العربية',
    'fa': 'فارسی',
    'he': 'עִבְרִית',
    'hi': 'हिन्दी',
    'th': 'ไทย',
    'ja': '日本語',
    'zh-CN': '中文（简体）',
    'zh-TW': '中文（繁體）',
    'ko': '한국어'
  };
  return languageMap[selectedLanguage.value] || selectedLanguage.value;
})

// 方法
const checkLoginStatus = async () => {
  try {
    const response = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
    if (response?.isLoggedIn) {
      isLoggedIn.value = true;
      const userInfo = await chrome.runtime.sendMessage({ type: 'getUserInfo' });
      if (userInfo.success && userInfo.user) {
        await updateAvatarImage(userInfo.user.picture_url);
        // 保存用户 ID
        const storage = await chrome.storage.sync.get('formify_settings');
        const settings = storage.formify_settings || {};
        settings.userId = userInfo.user.id;
        await chrome.storage.sync.set({ formify_settings: settings });
      }
    }
    showLoginPrompt.value = !response?.isLoggedIn;
  } catch (error) {
    console.error('Error checking login status:', error);
    isLoggedIn.value = false;
  }
}

const loadProjects = async () => {
  try {
    const result = await chrome.storage.sync.get(STORAGE_KEYS.PROJECTS)
    projects.value = result[STORAGE_KEYS.PROJECTS] || []
  } catch (error) {
    console.error('Error loading projects:', error)
    showStatusMessage('Failed to load projects', 'error')
  }
}

const handleModeChange = (mode: 'general' | 'email' | 'bugReport') => {
  currentMode.value = mode
  chrome.storage.sync.set({ [STORAGE_KEYS.LAST_MODE]: mode })
}

const showStatusMessage = (message: string, type: 'info' | 'error' | 'success' = 'info') => {
  statusMessage.value = message
  statusType.value = type
  showStatus.value = true
  setTimeout(() => {
    showStatus.value = false
  }, 3000)
}

const handleFormSubmit = async () => {
  if (!description.value.trim()) {
    showStatusMessage('Please enter a description', 'error')
    return
  }

  try {
    isLoading.value = true

    // 1. 首先获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
    if (!tab?.id) {
      throw new Error('No active tab found')
    }

    // 2. 检查页面状态并获取表单字段
    const pageStatus = await chrome.tabs.sendMessage(tab.id, { type: 'checkPageStatus' })
    // Page status checked

    if (!pageStatus?.isValid) {
      throw new Error('Invalid page status')
    }

    if (pageStatus.needsRefresh) {
      throw new Error('Please refresh the page')
    }

    if (!pageStatus.hasFormFields) {
      throw new Error('No form fields found on this page')
    }

    // 获取表单字段
    const formFieldsResponse = await chrome.tabs.sendMessage(tab.id, {
      action: 'getFormFields'
    })

    if (!formFieldsResponse?.fields) {
      throw new Error('Failed to get form fields')
    }

    // 3. 准备发送 AI 请求
    // 先通知content脚本显示生成中的视觉效果
    await chrome.tabs.sendMessage(tab.id, {
      action: 'showGeneratingEffect'
    })

    const aiResponse = await chrome.runtime.sendMessage({
      type: 'aiRequest',
      prompt: description.value,
      options: {
        mode: currentMode.value,
        projectId: selectedProject.value,
        language: selectedLanguage.value,
        description: description.value,
        formFields: formFieldsResponse.fields
      }
    })

    // AI response received

    if (!aiResponse?.success) {
      throw new Error(aiResponse?.error || 'Failed to generate content')
    }

    // 4. 发送填充请求
    // Preparing to fill form with AI response data
    const fillResponse = await chrome.tabs.sendMessage(tab.id, {
      action: 'fillForm',
      data: aiResponse.data
    })

    // Form fill response received

    if (fillResponse?.success) {
      // 立即结束 loading 动效
      isLoading.value = false
      // 设置成功状态并创建彩带效果
      isSuccess.value = true
      createConfetti()
      // 5秒后重置成功状态
      setTimeout(() => {
        isSuccess.value = false
      }, 5000)
    } else {
      throw new Error(fillResponse?.error || 'Failed to fill form')
    }
  } catch (error: any) {
    // Form submission error occurred
    showStatusMessage(error.message || 'Failed to fill form', 'error')
    isLoading.value = false
  }
}

const handleSignIn = () => {
  chrome.tabs.create({ url: 'https://fillify.tech/signin' })
  window.close()
}

const handleSkipLogin = async () => {
  try {
    // 通知 background script 设置跳过登录状态
    await chrome.runtime.sendMessage({
      type: 'setSkipLogin',
      skip: true
    });
    showLoginPrompt.value = false;
  } catch (error) {
    console.error('Error setting skip login:', error);
  }
}

const handleSignOut = async () => {
  try {
    await fetch('https://fillify-343190162770.asia-east1.run.app/api/auth/logout', {
      method: 'POST',
      credentials: 'include'
    });
    isLoggedIn.value = false;
    showLoginPrompt.value = true;
  } catch (error) {
    // Error occurred during sign out
    showStatusMessage('Failed to sign out', 'error');
  }
}

const openSettings = () => {
  chrome.tabs.create({
    url: chrome.runtime.getURL('settings.html')
  })
  window.close()
}

const openProjectSettings = () => {
  chrome.tabs.create({
    url: chrome.runtime.getURL('settings.html?tab=library&action=add_project')
  })
  window.close()
}

const handleLanguageChange = () => {
  chrome.storage.local.set({
    [STORAGE_KEYS.LAST_LANGUAGE]: selectedLanguage.value
  })
}

// confetti 动画实现
const createConfetti = () => {
  if (!confettiCanvas.value) return
  const canvas = confettiCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  let confetti: Confetto[] = []
  let sequins: Sequin[] = []
  let animationFrame: number | null = null

  const initConfettoVelocity = (xRange: [number, number], yRange: [number, number]) => {
    const x = randomRange(xRange[0], xRange[1])
    const range = yRange[1] - yRange[0] + 1
    let y = yRange[1] - Math.abs(randomRange(0, range) + randomRange(0, range) - range)
    if (y >= yRange[1] - 1) {
      y += Math.random() < 0.25 ? randomRange(1, 3) : 0
    }
    return { x: x, y: -y }
  }

  class Confetto {
    randomModifier: number;
    color: { front: string; back: string };
    dimensions: { x: number; y: number };
    position: { x: number; y: number };
    rotation: number;
    scale: { x: number; y: number };
    velocity: { x: number; y: number };

    constructor() {
      const button = document.getElementById('fill-button')
      const buttonRect = button?.getBoundingClientRect()
      const buttonCenterX = buttonRect ? buttonRect.left + buttonRect.width / 2 : window.innerWidth / 2
      const buttonTop = buttonRect ? buttonRect.top : window.innerHeight / 2

      this.randomModifier = randomRange(0, 99)
      this.color = confettiConfig.colors[Math.floor(randomRange(0, confettiConfig.colors.length))]
      this.dimensions = { x: randomRange(5, 9), y: randomRange(8, 15) }
      this.position = {
        x: buttonCenterX + randomRange(-10, 10),
        y: buttonTop + randomRange(-5, 5)
      }
      this.rotation = randomRange(0, 2 * Math.PI)
      this.scale = { x: 1, y: 1 }
      this.velocity = initConfettoVelocity([-9, 9], [6, 11])
    }

    update() {
      this.velocity.x -= this.velocity.x * confettiConfig.dragConfetti
      this.velocity.y = Math.min(this.velocity.y + confettiConfig.gravityConfetti, confettiConfig.terminalVelocity)
      this.position.x += this.velocity.x
      this.position.y += this.velocity.y
      this.scale.y = Math.cos((this.position.y + this.randomModifier) * 0.09)
    }
  }

  class Sequin {
    color: string;
    radius: number;
    position: { x: number; y: number };
    velocity: { x: number; y: number };

    constructor() {
      const button = document.getElementById('fill-button')
      const buttonRect = button?.getBoundingClientRect()
      const buttonCenterX = buttonRect ? buttonRect.left + buttonRect.width / 2 : window.innerWidth / 2
      const buttonTop = buttonRect ? buttonRect.top : window.innerHeight / 2

      this.color = confettiConfig.colors[Math.floor(randomRange(0, confettiConfig.colors.length))].back
      this.radius = randomRange(1, 2)
      this.position = {
        x: buttonCenterX + randomRange(-15, 15),
        y: buttonTop + randomRange(-5, 5)
      }
      this.velocity = {
        x: randomRange(-15, 15),
        y: randomRange(1, 5)
      }
    }

    update() {
      this.velocity.x -= this.velocity.x * confettiConfig.dragSequins
      this.velocity.y = Math.min(this.velocity.y + confettiConfig.gravitySequins, confettiConfig.terminalVelocity)
      this.position.x += this.velocity.x
      this.position.y += this.velocity.y
    }
  }

  const initBurst = () => {
    for (let i = 0; i < confettiConfig.confettiCount; i++) {
      confetti.push(new Confetto())
    }
    for (let i = 0; i < confettiConfig.sequinCount; i++) {
      sequins.push(new Sequin())
    }
  }

  const render = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    confetti.forEach((confetto, index) => {
      confetto.update()
      ctx.fillStyle = confetto.scale.y > 0 ? confetto.color.front : confetto.color.back
      ctx.save()
      ctx.translate(confetto.position.x, confetto.position.y)
      ctx.rotate(confetto.rotation)
      ctx.fillRect(-confetto.dimensions.x / 2, -confetto.dimensions.y / 2, confetto.dimensions.x, confetto.dimensions.y)
      ctx.restore()
      if (confetto.position.y >= canvas.height + 100) confetti.splice(index, 1)
    })

    sequins.forEach((sequin, index) => {
      sequin.update()
      ctx.fillStyle = sequin.color
      ctx.beginPath()
      ctx.arc(sequin.position.x, sequin.position.y, sequin.radius, 0, 2 * Math.PI)
      ctx.fill()
      if (sequin.position.y >= canvas.height + 100) sequins.splice(index, 1)
    })

    if (confetti.length === 0 && sequins.length === 0) {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
        animationFrame = null
      }
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      return
    }

    animationFrame = requestAnimationFrame(render)
  }

  const resizeCanvas = () => {
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
  }

  resizeCanvas()
  window.addEventListener('resize', resizeCanvas)

  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
  initBurst()
  render()
}

// 辅助函数
const randomRange = (min: number, max: number) => {
  return Math.random() * (max - min) + min
}

// 添加登录页面跳转方法
const openLoginPage = () => {
  chrome.tabs.create({ url: 'https://fillify.tech/signin' })
  window.close() // 关闭 popup
}

// 需要补充用户头像更新逻辑
const updateAvatarImage = async (pictureUrl: string) => {
  // 从 popup.js 移植头像加载和错误处理逻辑
  const loadImage = (url: string) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = url;
    });
  };

  try {
    await loadImage(pictureUrl);
    // 更新用户头像 URL
    userAvatarUrl.value = pictureUrl;
  } catch (error) {
    // Error loading avatar image
    userAvatarUrl.value = 'path/to/default-avatar.png';
  }
}

// 检查页面状态和表单字段
const checkFormFields = async () => {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab?.id) {
      throw new Error('No active tab found');
    }

    // 首先检查页面状态
    const pageStatus = await sendMessageWithRetry<PageStatus>(tab.id, {
      type: 'checkPageStatus'
    });

    if (!pageStatus.isValid) {
      throw new Error('Invalid page status');
    }

    if (pageStatus.needsRefresh) {
      showStatusMessage('Please refresh the page', 'error');
      hasFormFields.value = false;
      return;
    }

    // 然后检查表单字段
    const response = await sendMessageWithRetry<FormFieldsResponse>(tab.id, {
      action: 'checkFormFields'
    });

    if (response && response.hasFields) {
      hasFormFields.value = true;
    } else {
      hasFormFields.value = false;
      showStatusMessage('No form fields found on this page', 'error');
    }
  } catch (error: any) {
    hasFormFields.value = false;
    if (error.message?.includes('Receiving end does not exist')) {
      showStatusMessage('Please refresh the page', 'error');
    } else {
      showStatusMessage('Unable to detect form fields', 'error');
    }
  }

  // 更新项目选择器可见性
  const projectSelectorElement = document.querySelector('.project-selector') as HTMLElement;
  if (projectSelectorElement) {
    const isBugReportMode = currentMode.value === 'bugReport';
    projectSelectorElement.style.display = isBugReportMode ? 'block' : 'none';
  }
}

// 更新重试机制
const sendMessageWithRetry = async <T>(tabId: number, message: any, maxRetries = 3, delay = 500): Promise<T> => {
  let lastError;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await new Promise<T>((resolve, reject) => {
        chrome.tabs.sendMessage(tabId, message, (response: T) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(response);
          }
        });
      });
    } catch (error) {
      lastError = error;
      // Retry attempt occurred
      if (i < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
        continue;
      }
    }
  }

  throw lastError;
};

// 添加语言选择初始化
const initializeLanguage = async () => {
  const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_LANGUAGE);
  selectedLanguage.value = result[STORAGE_KEYS.LAST_LANGUAGE] || 'auto';
}

// 修改初始化逻辑
onMounted(async () => {
  try {
    // 先检查登录状态
    const response = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
    showLoginPrompt.value = !response?.skipLogin && !response?.isLoggedIn;
    isLoggedIn.value = response?.isLoggedIn || false;

    if (response?.isLoggedIn) {
      const userInfo = await chrome.runtime.sendMessage({ type: 'getUserInfo' });
      if (userInfo.success && userInfo.user) {
        await updateAvatarImage(userInfo.user.picture_url);
      }
    }

    await loadProjects();
    await initializeLanguage();

    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) throw new Error('No active tab found');

    // 检查页面状态，使用改进的重试机制
    const pageStatusResponse = await sendMessageWithRetry<PageStatus>(tab.id, { type: 'checkPageStatus' })
      .catch((error) => {
        // Error checking page status
        return {
          isValid: false,
          needsRefresh: true,
          hasFormFields: false,
          error: error.message
        } as PageStatus;
      });

    // 根据检测结果更新状态
    if (!pageStatusResponse.isValid) {
      if (pageStatusResponse.error?.includes('Receiving end does not exist')) {
        showStatusMessage('Please refresh the page', 'error');
      } else {
        showStatusMessage('Unable to check page status', 'error');
      }
      hasFormFields.value = false;
    } else {
      // 只在确实需要刷新时才提示
      if (pageStatusResponse.needsRefresh && !pageStatusResponse.hasFormFields) {
        showStatusMessage('Please refresh the page', 'error');
        hasFormFields.value = false;
      } else {
        hasFormFields.value = pageStatusResponse.hasFormFields;
        if (!hasFormFields.value) {
          showStatusMessage('No form fields found on this page', 'error');
        }
      }
    }
  } catch (error) {
    // Error during initialization
    hasFormFields.value = false
    showStatusMessage('Unable to check page status', 'error')
  }

  // 监听存储变化
  chrome.storage.onChanged.addListener((changes) => {
    if (changes[STORAGE_KEYS.PROJECTS]) {
      loadProjects();
    }
  });
});

// Credits 相关功能（当前禁用）
const fetchCreditStats = async (userId: string) => {
  return null; // 临时禁用
}

const updateCreditsDisplay = async (userId: string) => {
  return; // 临时禁用
}

// 添加鼠标滚轮切换模式功能
const handleModeWheel = (event: WheelEvent) => {
  // 检测是否是触控板滚动
  if (event.deltaY % 120 !== 0) { // 触控板的 wheelDelta 通常不是 120 的倍数
    return; // 忽略触控板滚动
  }

  event.preventDefault();

  const modes = ['general', 'email', 'bugReport'] as const;
  const currentIndex = modes.indexOf(currentMode.value);

  // 根据滚轮方向决定切换方向
  const direction = event.deltaY > 0 ? 1 : -1;
  let nextIndex = currentIndex + direction;

  // 循环切换
  if (nextIndex >= modes.length) nextIndex = 0;
  if (nextIndex < 0) nextIndex = modes.length - 1;

  handleModeChange(modes[nextIndex]);
}

// 添加方法
const openDashboard = () => {
  chrome.tabs.create({ url: 'https://fillify.tech/dashboard' });
}
</script>

<template>
  <div class="container">
    <!-- 登录提示页面 -->
    <div id="login-prompt" class="login-prompt" v-show="showLoginPrompt">
      <div class="login-prompt-content">
        <div class="logo-container">
          <!-- SVG Logo 保持不变 -->
          <svg class="login-logo" width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M814.311 75H125C97.3858 75 75 97.3858 75 125V918C75 945.614 97.3858 968 125 968H905.481C933.096 968 955.481 945.614 955.481 918V216.17L814.311 75Z" fill="#1D5DF4"/>
            <g style="mix-blend-mode:hard-light">
              <path d="M956 217H814V75L885 146L956 217Z" fill="#D9D9D9"/>
            </g>
            <rect x="340.211" y="344.847" width="504.457" height="81.6033" fill="white"/>
            <rect x="340.211" y="508.054" width="504.457" height="81.6033" fill="white"/>
            <rect x="340.211" y="671.261" width="504.457" height="81.6033" fill="white"/>
            <path d="M245.625 333.72L260.152 372.977L299.409 387.504L260.152 402.03L245.625 441.288L231.099 402.03L191.841 387.504L231.099 372.977L245.625 333.72Z" fill="white"/>
            <path d="M245.625 496.926L260.152 536.184L299.409 550.71L260.152 565.237L245.625 604.494L231.099 565.237L191.841 550.71L231.099 536.184L245.625 496.926Z" fill="white"/>
            <path d="M245.625 660.133L260.152 699.39L299.409 713.917L260.152 728.443L245.625 767.701L231.099 728.443L191.841 713.917L231.099 699.39L245.625 660.133Z" fill="white"/>
          </svg>
        </div>
        <h2>Welcome to Fillify</h2>
        <p>Please sign in to access all features</p>
        <button class="sign-in-btn" @click="handleSignIn">Sign in</button>
        <a href="#" class="skip-login-text" @click.prevent="handleSkipLogin">Skip sign in</a>
      </div>
    </div>

    <div id="main-content" v-show="!showLoginPrompt">
      <div class="header">
        <h1>Fillify</h1>
        <div class="header-right">
          <div class="login-status">
            <div class="avatar-menu">
              <template v-if="isLoggedIn">
                <img
                  v-if="userAvatarUrl"
                  :src="userAvatarUrl"
                  class="user-avatar"
                  style="width: 24px; height: 24px; border-radius: 50%; cursor: pointer;"
                  title="Go to Dashboard"
                  @click="openDashboard"
                />
                <div class="menu-popover">
                  <div class="menu-item sign-out" @click="handleSignOut">Sign out</div>
                </div>
              </template>
              <div v-else style="cursor: pointer;" title="Sign in" @click="openLoginPage">
                <svg width="24" height="24" viewBox="0 0 1024 1024" style="vertical-align: middle;">
                  <path d="M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64zM384.8 376c4-64 56-115.2 120-119.2 74.4-4 135.2 55.2 135.2 128 0 70.4-57.6 128-128 128-73.6 0-132-62.4-127.2-136.8zM768 746.4c0 12-9.6 21.6-21.6 21.6H278.4c-12 0-21.6-9.6-21.6-21.6v-64c0-84.8 170.4-128 255.2-128 84.8 0 255.2 42.4 255.2 128l0.8 64z" fill="#333333"/>
                </svg>
              </div>
            </div>
          </div>
          <button class="settings-button" @click="openSettings" title="Settings">
            <div class="menu-icon">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>
        </div>
      </div>

      <form id="form-config" @submit.prevent="handleFormSubmit">
        <!-- Mode buttons -->
        <div class="mode-buttons" @wheel="handleModeWheel">
          <button
            type="button"
            id="general-mode"
            class="mode-btn"
            :class="{ active: currentMode === 'general' }"
            @click="handleModeChange('general')"
          >General</button>
          <button
            type="button"
            id="email-mode"
            class="mode-btn"
            :class="{ active: currentMode === 'email' }"
            @click="handleModeChange('email')"
          >Email</button>
          <button
            type="button"
            id="bug-report-mode"
            class="mode-btn"
            :class="{ active: currentMode === 'bugReport' }"
            @click="handleModeChange('bugReport')"
          >Bug Report</button>
        </div>

        <div class="project-selector" v-show="showProjectSelector">
          <div class="select-wrapper">
            <select v-model="selectedProject" :disabled="isInputDisabled">
              <option value="">Select any project (Optional)</option>
              <option v-for="project in projects" :key="project.id" :value="project.id">
                {{ project.name }}
              </option>
            </select>
            <button type="button" class="add-project-btn" @click="openProjectSettings">+</button>
          </div>
        </div>

        <div class="description-input">
          <div class="textarea-wrapper">
            <textarea
              v-model="description"
              :placeholder="textareaPlaceholder"
              :disabled="isInputDisabled"
              rows="4"
              required
            ></textarea>
          </div>
          <div class="language-selector">
            <div class="flex gap-1 text-base items-center font-normal mx-2 h-[26px] text-fg-02-light cursor-pointer relative block" title="Select output language">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4">
                <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3.6001 9H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3.6001 15H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 21C13.6569 21 15 16.9706 15 12C15 7.02944 13.6569 3 12 3C10.3432 3 9 7.02944 9 12C9 16.9706 10.3432 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <div class="selected-language">{{ selectedLanguageText }}</div>
              <select v-model="selectedLanguage" @change="handleLanguageChange">
                <option value="auto">Auto</option>
                <option value="id">Bahasa Indonesia</option>
                <option value="ms">Bahasa Melayu</option>
                <option value="da">Dansk</option>
                <option value="de">Deutsch</option>
                <option value="en">English</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="it">Italiano</option>
                <option value="nl">Nederlands</option>
                <option value="no">Norsk</option>
                <option value="pl">Polski</option>
                <option value="pt">Português</option>
                <option value="ro">Română</option>
                <option value="fi">Suomi</option>
                <option value="sv">Svenska</option>
                <option value="vi">Tiếng Việt</option>
                <option value="tr">Türkçe</option>
                <option value="hu">Magyar</option>
                <option value="cs">Čeština</option>
                <option value="uk">Українська</option>
                <option value="ru">Русский</option>
                <option value="bg">Български</option>
                <option value="ar">العربية</option>
                <option value="fa">فارسی</option>
                <option value="he">עִבְרִית</option>
                <option value="hi">हिन्दी</option>
                <option value="th">ไทย</option>
                <option value="ja">日本語</option>
                <option value="zh-CN">中文（简体）</option>
                <option value="zh-TW">中文（繁體）</option>
                <option value="ko">한국어</option>
              </select>
            </div>
          </div>
        </div>

        <button
          id="fill-button"
          type="submit"
          class="primary-btn"
          :class="{ loading: isLoading, success: isSuccess }"
          :disabled="isButtonDisabled"
        >
          <div class="button-content">
            <span class="button-text">{{ buttonText }}</span>
            <svg
              class="sparkle-icon"
              :class="{ hidden: isLoading || isSuccess }"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846a.75.75 0 011.442 0l.813 2.846a.75.75 0 001.442.384l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
            </svg>
          </div>
          <div class="animation" v-if="isLoading"></div>
        </button>
      </form>

      <canvas
        id="confetti-canvas"
        ref="confettiCanvas"
        style="position: absolute; top: 0; left: 0; pointer-events: none; z-index: 1000;"
      ></canvas>

      <div id="status-container">
        <div
          id="status-message"
          class="status-message"
          :class="{ show: showStatus, [statusType]: true }"
        >{{ statusMessage }}</div>
      </div>
    </div>
  </div>
</template>

<style>
/* 从 styles.css 导入全局样式 */
.hidden {
  display: none;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

@keyframes formifyBorderGlow {
  0% {
    outline: 2px solid rgba(33, 150, 243, 0.4);
    box-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
  }
  50% {
    outline: 2px solid rgba(33, 150, 243, 0.8);
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);
  }
  100% {
    outline: 2px solid rgba(33, 150, 243, 0.4);
    box-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
  }
}

.formify-loading {
  animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
  z-index: 9999;
}

/* 添加容器相对定位 */
.container {
  position: relative;
  overflow: hidden;  /* 确保粒子不会溢出 */
}

/* 禁用状态样式 */
textarea:disabled {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

.project-selector select:disabled {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed !important;
}
</style>
