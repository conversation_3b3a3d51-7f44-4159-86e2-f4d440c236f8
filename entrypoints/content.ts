export default defineContentScript({
  matches: ['<all_urls>'],  // 匹配所有 URL
  main() {
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setupMessageListener();
        setupInPagePopup();
      });
    } else {
      setupMessageListener();
      setupInPagePopup();
    }

    Logger.debug('form', 'Content script initialized')
  }
})

// 类型定义
interface FormField {
  element: HTMLElement
  type: string
  id: string
  name: string
  placeholder?: string
  label?: string
}

interface Logger {
  _log: (type: 'info' | 'success' | 'error' | 'debug', category: string, message: string, data?: any) => void
  info: (category: string, message: string, data?: any) => void
  success: (category: string, message: string, data?: any) => void
  error: (message: string, error: any) => void
  debug: (category: string, message: string, data?: any) => void
}

interface Debug {
  enabled: boolean
  form: boolean
  fill: boolean
  api: boolean
  message: boolean
  [key: string]: boolean  // 添加索引签名
}

interface StorageKeys {
  LAST_MODE: string
  PROJECTS: string
  SKIP_LOGIN: string
  LAST_LANGUAGE: string
}

interface FormContent {
  to?: string
  recipient?: string
  subject?: string
  body?: string
  content?: string
  title?: string
  description?: string
  [key: string]: string | undefined
}

interface FillFormCallback {
  success?: () => void;
  error?: (error: any) => void;
}

// Constants
const STORAGE_KEYS: StorageKeys = {
  LAST_MODE: 'formify_last_mode',
  PROJECTS: 'formify_projects',
  SKIP_LOGIN: 'formify_skip_login',
  LAST_LANGUAGE: 'formify_last_language'
}

// 屏蔽特定错误消息
const errorMessages: string[] = [
  'Extension context invalidated',
  'Could not establish connection. Receiving end does not exist',
  'The message port closed before a response was received'
]

// Debug configuration
const DEBUG: Debug = {
  enabled: false,    // 关闭总开关
  form: false,       // 关闭表单检测相关日志
  fill: false,       // 关闭表单填充相关日志
  api: false,        // 关闭API响应相关日志
  message: false     // 关闭消息相关日志
}

// 重写 console.error 来屏蔽特定错误
const originalConsoleError = console.error
console.error = function(...args: any[]) {
  const shouldBlock = errorMessages.some(msg =>
    args.some(arg =>
      arg instanceof Error ? arg.message.includes(msg) : String(arg).includes(msg)
    )
  )

  if (!shouldBlock) {
    originalConsoleError.apply(console, args)
  }
}

// Logger utility
const Logger: Logger = {
  _log: (type, category, message, data = null) => {
    if (!DEBUG.enabled || !DEBUG[category]) return

    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      debug: '🔍'
    }

    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '')
  },

  info: (category, message, data = null) => {
    Logger._log('info', category, message, data)
  },

  success: (category, message, data = null) => {
    Logger._log('success', category, message, data)
  },

  error: (message, error) => {
    // 错误日志始终输出
    console.error(`[Formify] ❌ ${message}`, error)
  },

  debug: (category, message, data = null) => {
    Logger._log('debug', category, message, data)
  }
}

// 添加邮件客户端配置
const emailClients = {
  gmail: {
    recipient: 'input.agP.aFw[role="combobox"], div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"], div[role="textbox"][aria-label*="收件者"], div[role="textbox"][aria-label*="宛先"]',
    subject: 'input.aoT[name="subjectbox"], input[aria-label*="Subject"], input[aria-label*="主题"], input[aria-label*="主旨"], input[aria-label*="件名"]',
    body: 'div.Am.aiL.Al.editable.LW-avf[contenteditable="true"], div[role="textbox"][aria-label="Message Body"], div[role="textbox"][aria-label*="正文"], div[role="textbox"][aria-label*="内容"], div[role="textbox"][aria-label*="本文"]'
  },
  outlook: {
    recipient: 'div[role="textbox"].EditorClass, div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"], div[role="textbox"][aria-label*="收件者"], div[role="textbox"][aria-label*="宛先"]',
    subject: 'input.fui-Input__input.zrkM_, input[aria-label*="Subject"], input[aria-label*="主题"], input[aria-label*="主旨"], input[aria-label*="件名"]',
    body: 'div[role="textbox"].DziEn, div[contenteditable="true"][aria-label*="Message body"], div[contenteditable="true"][aria-label*="正文"], div[contenteditable="true"][aria-label*="内容"], div[contenteditable="true"][aria-label*="本文"]'
  },
  yahoo: {
    recipient: 'input#message-to-field, input[placeholder*="To"], input[placeholder*="收件人"]',
    subject: 'input#mail-subject, input[placeholder*="Subject"], input[placeholder*="主题"]',
    body: 'div[contenteditable="true"][aria-label="Message body"], div[contenteditable="true"][aria-label*="正文"]'
  },
  protonmail: {
    recipient: 'input[placeholder="Email addresses"], input[placeholder*="To"], input[placeholder*="收件人"]',
    subject: 'input[placeholder="Subject"], input[placeholder*="主题"]',
    body: 'div[contenteditable="true"].ProseMirror, div[contenteditable="true"][aria-label*="body"]'
  }
} as const;

// 更新 detectFormFields 函数
function detectFormFields(): FormField[] {
  const formFields: FormField[] = [];

  Logger.debug('form', 'Starting form field detection');

  // 检测邮件客户端
  Object.entries(emailClients).forEach(([client, selectors]) => {
    Object.entries(selectors).forEach(([fieldType, selector]) => {
      const element = document.querySelector(selector);
      if (element) {
        formFields.push({
          element: element as HTMLElement,
          type: element.tagName.toLowerCase(),
          id: element.id,
          name: fieldType,
          placeholder: (element as HTMLInputElement).placeholder || '',
          label: fieldType.charAt(0).toUpperCase() + fieldType.slice(1)
        });
      }
    });
  });

  // 如果没有找到特定的邮件客户端字段，继续检查其他字段
  if (formFields.length === 0) {
    // 获取所有可能的表单元素
    const allTextboxes = document.querySelectorAll('[role="textbox"]');
    const allInputs = document.querySelectorAll('input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"])');
    const allTextareas = document.querySelectorAll('textarea');
    const allSelects = document.querySelectorAll('select');
    const allContentEditables = document.querySelectorAll('[contenteditable="true"]');
    const allEditorFrames = document.querySelectorAll('iframe.cke_wysiwyg_frame, iframe.tox-edit-area__iframe');

    Logger.debug('form', 'Found base elements count', {
      textboxes: allTextboxes.length,
      inputs: allInputs.length,
      textareas: allTextareas.length,
      selects: allSelects.length,
      contentEditables: allContentEditables.length,
      editorFrames: allEditorFrames.length
    });

    // 检查是否是 GitHub 页面
    if (window.location.hostname.includes('github.com')) {
      Logger.debug('form', 'Detected GitHub page, using generic form detection');

      // 使用通用的 GitHub Issue 表单元素查找函数
      const { titleInput, descriptionInput, taskList } = findGitHubIssueFormElements();

      // 将找到的元素添加到表单字段列表
      if (titleInput) {
        formFields.push({
          element: titleInput,
          type: 'text',
          id: titleInput.id,
          name: 'title',
          placeholder: titleInput.getAttribute('placeholder') || '',
          label: titleInput.getAttribute('aria-label') || 'Title'
        });
      }

      if (descriptionInput) {
        formFields.push({
          element: descriptionInput,
          type: descriptionInput.tagName.toLowerCase() === 'textarea' ? 'textarea' : 'contenteditable',
          id: descriptionInput.id,
          name: 'description',
          placeholder: descriptionInput.getAttribute('placeholder') || '',
          label: 'Description'
        });
      }

      if (taskList) {
        formFields.push({
          element: taskList,
          type: 'tasklist',
          id: taskList.id,
          name: 'tasklist',
          placeholder: '',
          label: 'Task List'
        });
      }
    }

    // 通用字段处理
    const commonInputs = Array.from(allInputs).filter(input => {
      const type = input.getAttribute('type');
      return type === 'text' || type === 'textarea' || type === 'email' ||
             type === 'tel' || type === 'url' || type === 'number' ||
             type === 'date' || type === 'search' || !type;
    });

    // 处理通用输入字段
    commonInputs.forEach(input => {
      const isDuplicate = formFields.some(field => field.element === input);
      if (!isDuplicate && isVisibleElement(input as HTMLElement)) {
        const label = findLabel(input as HTMLElement);
        formFields.push({
          element: input as HTMLElement,
          type: input.getAttribute('type') || 'text',
          id: input.id,
          name: input.getAttribute('name') || input.id || getNameFromAttributes(input),
          placeholder: input.getAttribute('placeholder') || '',
          label
        });
      }
    });

    // 处理 textarea 元素
    Array.from(allTextareas).forEach(textarea => {
      const isDuplicate = formFields.some(field => field.element === textarea);
      if (!isDuplicate && isVisibleElement(textarea as HTMLElement)) {
        const label = findLabel(textarea as HTMLElement);
        formFields.push({
          element: textarea as HTMLElement,
          type: 'textarea',
          id: textarea.id,
          name: textarea.getAttribute('name') || textarea.id || getNameFromAttributes(textarea),
          placeholder: textarea.getAttribute('placeholder') || '',
          label
        });
      }
    });

    // 处理 select 元素
    Array.from(allSelects).forEach(select => {
      const isDuplicate = formFields.some(field => field.element === select);
      if (!isDuplicate && isVisibleElement(select as HTMLElement)) {
        const label = findLabel(select as HTMLElement);
        formFields.push({
          element: select as HTMLElement,
          type: 'select',
          id: select.id,
          name: select.getAttribute('name') || select.id || getNameFromAttributes(select),
          placeholder: '',
          label
        });
      }
    });

    // 处理 contenteditable 元素
    Array.from(allContentEditables).forEach(element => {
      const isDuplicate = formFields.some(field => field.element === element);
      if (!isDuplicate && isVisibleElement(element as HTMLElement)) {
        const label = findLabel(element as HTMLElement);
        formFields.push({
          element: element as HTMLElement,
          type: 'contenteditable',
          id: element.id,
          name: element.getAttribute('name') || element.id || getNameFromAttributes(element),
          placeholder: '',
          label
        });
      }
    });

    // 处理编辑器 iframe
    Array.from(allEditorFrames).forEach(iframe => {
      const isDuplicate = formFields.some(field => field.element === iframe);
      if (!isDuplicate && isVisibleElement(iframe as HTMLElement)) {
        const label = findLabel(iframe as HTMLElement);
        formFields.push({
          element: iframe as HTMLElement,
          type: 'iframe-editor',
          id: iframe.id,
          name: iframe.getAttribute('name') || iframe.id || 'editor',
          placeholder: '',
          label: label || 'Editor'
        });
      }
    });
  }

  Logger.debug('form', 'Detected form fields:', formFields);
  return formFields;
}

// 检查元素是否可见
function isVisibleElement(element: HTMLElement): boolean {
  const style = window.getComputedStyle(element);
  return style.display !== 'none' &&
         style.visibility !== 'hidden' &&
         style.opacity !== '0' &&
         element.offsetWidth > 0 &&
         element.offsetHeight > 0;
}

// 通用的 GitHub Issue 表单元素查找函数
function findGitHubIssueFormElements(): {
  titleInput: HTMLElement | null,
  descriptionInput: HTMLElement | null,
  taskList: HTMLElement | null
} {
  Logger.debug('form', 'Using generic GitHub issue form detection');

  let titleInput: HTMLElement | null = null;
  let descriptionInput: HTMLElement | null = null;
  let taskList: HTMLElement | null = null;

  // 1. 基于页面上下文检测 GitHub issue 页面
  const isIssuePage = window.location.pathname.includes('/issues/') ||
                     window.location.pathname.includes('/issues/new') ||
                     window.location.pathname.includes('/pull/') ||
                     window.location.pathname.includes('/pull/new');

  if (!isIssuePage) {
    Logger.debug('form', 'Not a GitHub issue page');
    return { titleInput, descriptionInput, taskList };
  }

  // 2. 查找表单容器
  const formContainer = document.querySelector('form.js-new-issue-form, .js-issues-listing, .js-new-comment-form');
  Logger.debug('form', 'Form container found:', !!formContainer);

  // 3. 查找标题输入框 - 使用多种策略
  // 策略 1: 基于语义和属性
  const titleCandidates = [
    // 基于语义
    document.querySelector('input[aria-label*="title" i], input[aria-label*="标题" i]'),
    // 基于 ID 和名称
    document.querySelector('input#issue_title, input[name="issue[title]"], input[name*="title" i]'),
    // 基于占位符
    document.querySelector('input[placeholder*="title" i], input[placeholder*="标题" i]')
  ].filter(Boolean);

  // 策略 2: 如果没有找到，查找页面上所有可见的文本输入框
  if (titleCandidates.length === 0) {
    const allTextInputs = document.querySelectorAll('input[type="text"]:not([hidden])');
    for (const input of Array.from(allTextInputs)) {
      if (isVisibleElement(input as HTMLElement)) {
        titleCandidates.push(input);
      }
    }
  }

  // 选择第一个可见的标题输入框
  for (const candidate of titleCandidates) {
    if (candidate && isVisibleElement(candidate as HTMLElement)) {
      titleInput = candidate as HTMLElement;
      break;
    }
  }

  // 4. 查找描述文本区域 - 使用多种策略
  // 策略 1: 基于语义和属性
  const descCandidates = [
    // 基于语义
    document.querySelector('textarea[aria-label*="body" i], textarea[aria-label*="description" i], textarea[aria-label*="markdown" i], textarea[aria-label*="内容" i], textarea[aria-label*="描述" i]'),
    // 基于 ID 和名称
    document.querySelector('textarea#issue_body, textarea[name="issue[body]"], textarea[name*="body" i], textarea[name*="description" i]'),
    // 基于占位符
    document.querySelector('textarea[placeholder*="description" i], textarea[placeholder*="write" i], textarea[placeholder*="描述" i], textarea[placeholder*="内容" i]'),
    // 基于角色
    document.querySelector('[role="textbox"][aria-label*="body" i], [role="textbox"][aria-label*="comment" i], [role="textbox"][data-gramm="false"]'),
    // 基于数据属性
    document.querySelector('textarea[data-resize], [data-testid*="body" i], [data-testid*="description" i]')
  ].filter(Boolean);

  // 策略 2: 如果没有找到，查找页面上所有可见的文本区域
  if (descCandidates.length === 0) {
    const allTextareas = document.querySelectorAll('textarea:not([hidden]), [role="textbox"], [contenteditable="true"]');
    for (const textarea of Array.from(allTextareas)) {
      if (isVisibleElement(textarea as HTMLElement)) {
        descCandidates.push(textarea);
      }
    }
  }

  // 选择第一个可见的描述文本区域
  for (const candidate of descCandidates) {
    if (candidate && isVisibleElement(candidate as HTMLElement)) {
      descriptionInput = candidate as HTMLElement;
      break;
    }
  }

  // 5. 查找任务列表
  taskList = document.querySelector('.task-list-item, .contains-task-list, .task-list') as HTMLElement;

  // 记录找到的元素
  Logger.debug('form', 'Generic GitHub form detection results:', {
    titleInput: titleInput ? {
      tagName: titleInput.tagName,
      id: titleInput.id,
      className: titleInput.className,
      attributes: {
        name: titleInput.getAttribute('name'),
        ariaLabel: titleInput.getAttribute('aria-label'),
        placeholder: titleInput.getAttribute('placeholder')
      }
    } : null,
    descriptionInput: descriptionInput ? {
      tagName: descriptionInput.tagName,
      id: descriptionInput.id,
      className: descriptionInput.className,
      attributes: {
        name: descriptionInput.getAttribute('name'),
        ariaLabel: descriptionInput.getAttribute('aria-label'),
        role: descriptionInput.getAttribute('role'),
        placeholder: descriptionInput.getAttribute('placeholder'),
        dataResize: descriptionInput.getAttribute('data-resize'),
        dataTestId: descriptionInput.getAttribute('data-testid')
      }
    } : null,
    taskList: !!taskList
  });

  return { titleInput, descriptionInput, taskList };
}

// 从其他属性中获取可能的名称
function getNameFromAttributes(element: Element): string {
  // 尝试从各种属性中获取名称
  return element.getAttribute('aria-label') ||
         element.getAttribute('data-name') ||
         element.getAttribute('data-field-name') ||
         element.getAttribute('data-testid') ||
         element.getAttribute('title') ||
         '';
}

// Label finding helper function
function findLabel(input: HTMLElement): string {
  // Try to find label by for attribute
  if (input.id) {
    const label = document.querySelector(`label[for="${input.id}"]`)
    if (label) return label.textContent?.trim() || ''
  }

  // Try to find parent label
  const parentLabel = input.closest('label')
  if (parentLabel) return parentLabel.textContent?.trim() || ''

  // Try to find nearby text that might be a label
  const previousElement = input.previousElementSibling
  if (previousElement && previousElement.tagName !== 'INPUT' && previousElement.tagName !== 'TEXTAREA') {
    return previousElement.textContent?.trim() || ''
  }

  return ''
}

// 添加一个全局变量来跟踪样式标签
let generatingStyleTag: HTMLStyleElement | null = null

// 显示生成中的视觉效果
function showGeneratingEffect() {
  try {
    // 如果已经有样式标签，先移除
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
    }

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      Logger.debug('effect', 'No form fields detected for showing effect')
      return
    }

    // 添加加载动画样式
    generatingStyleTag = document.createElement('style')
    generatingStyleTag.textContent = `
      @keyframes formifyBorderGlow {
        0% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
        50% { outline: 2px solid rgba(33, 150, 243, 0.8); box-shadow: 0 0 15px rgba(33, 150, 243, 0.6); }
        100% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
      }
      .formify-loading {
        animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
        z-index: 9999;
        position: relative;
      }
    `
    document.head.appendChild(generatingStyleTag)

    // 给所有表单字段添加加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.add('formify-loading')
      }
    })

    Logger.debug('effect', 'Added generating effect to form fields')
  } catch (error) {
    Logger.error('Error showing generating effect:', error)
  }
}

// 移除生成中的视觉效果
function removeGeneratingEffect() {
  try {
    // 移除所有加载动画
    document.querySelectorAll('.formify-loading').forEach(el => {
      el.classList.remove('formify-loading')
    })

    // 移除样式标签
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
      generatingStyleTag = null
    }

    Logger.debug('effect', 'Removed generating effect from form fields')
  } catch (error) {
    Logger.error('Error removing generating effect:', error)
  }
}

// 更新 fillForm 函数
function fillForm(formContent: FormContent, callback?: FillFormCallback): void {
  try {
    Logger.debug('fill', 'Starting form fill with content:', formContent)

    // 检查是否需要解析 JSON
    let contentToUse = formContent

    // 记录原始内容结构
    const getPreview = (content: any): string => {
      if (typeof content === 'string') {
        return content.substring(0, 100);
      } else if (typeof content === 'object' && content !== null) {
        return JSON.stringify(content).substring(0, 100);
      } else {
        return 'Not a string';
      }
    };

    Logger.debug('fill', 'Original form content structure:', {
      type: typeof formContent,
      isObject: typeof formContent === 'object',
      hasContentProperty: typeof formContent === 'object' && 'content' in formContent,
      contentType: typeof formContent === 'object' && formContent.content ? typeof formContent.content : 'N/A',
      keys: typeof formContent === 'object' ? Object.keys(formContent) : [],
      preview: getPreview(formContent)
    });

    if (typeof formContent === 'object' && formContent.content && typeof formContent.content === 'string') {
      try {
        Logger.debug('fill', 'Attempting to parse JSON content:', formContent.content.substring(0, 200) + '...')
        const parsedContent = JSON.parse(formContent.content)
        Logger.debug('fill', 'Successfully parsed JSON content:', {
          type: typeof parsedContent,
          isObject: typeof parsedContent === 'object',
          keys: typeof parsedContent === 'object' ? Object.keys(parsedContent) : [],
          preview: JSON.stringify(parsedContent).substring(0, 200) + '...'
        })
        contentToUse = parsedContent
      } catch (e) {
        Logger.error('Failed to parse JSON content:', e)
        Logger.debug('fill', 'Using original content due to JSON parse failure')
        // 如果解析失败，继续使用原始内容
      }
    }

    Logger.debug('fill', 'Form content structure:', {
      hasContent: !!contentToUse,
      contentType: typeof contentToUse,
      keys: contentToUse ? Object.keys(contentToUse) : [],
      values: contentToUse ? Object.entries(contentToUse).map(([key, value]) => ({
        key,
        type: typeof value,
        hasValue: !!value,
        valuePreview: typeof value === 'string' ? value.substring(0, 50) : value,
        fullValue: value // 添加完整值的输出
      })) : []
    })

    // 移除生成中的视觉效果
    removeGeneratingEffect()

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      throw new Error('No form fields detected')
    }

    // 先处理主题字段
    const subjectField = formFields.find(field => field.name === 'subject')
    if (subjectField && subjectField.element && contentToUse.subject) {
      Logger.debug('fill', 'Processing subject field...', { value: contentToUse.subject })
      try {
        // 检查是否是 React 组件
        const isReactComponent = subjectField.element.classList.contains('fui-Input__input') ||
                               subjectField.element.hasAttribute('data-reactid') ||
                               !!subjectField.element.closest('[data-reactroot]')

        if (isReactComponent) {
          // 使用 React 组件的处理方式
          if (subjectField.element instanceof HTMLInputElement) {
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              window.HTMLInputElement.prototype,
              "value"
            )?.set;

            if (nativeInputValueSetter) {
              // 调用setter设置值（忽略返回值）
              void nativeInputValueSetter.call(subjectField.element, contentToUse.subject);

              // 触发一系列事件来确保 React 状态更新
              ['input', 'change', 'blur'].forEach(eventType => {
                const event = new Event(eventType, {
                  bubbles: true,
                  composed: true,
                  cancelable: true
                })
                subjectField.element.dispatchEvent(event)
              });

              // 确保元素获得焦点
              subjectField.element.focus()

              // 创建并触发输入事件
              const inputEvent = new InputEvent('input', {
                bubbles: true,
                cancelable: true,
                inputType: 'insertText',
                data: contentToUse.subject
              })
              subjectField.element.dispatchEvent(inputEvent)

              // 最后失去焦点
              subjectField.element.blur()

              Logger.debug('fill', 'Filled React input component')
            }
          }
        } else {
          // 如果不是 React 组件，使用普通方式
          fillFieldContent(subjectField, contentToUse.subject)
        }
      } catch (e) {
        Logger.error('Error filling subject field:', e)
      }
    }

    // 处理其他字段
    formFields.forEach(field => {
      // 跳过主题字段，因为已经处理过了
      if (field.name === 'subject') return

      try {
        let content: string | undefined

        // Email mode specific field matching
        if (field.name === 'recipient' || field.name === 'to') {
          // Enhanced email recipient matching
          content = contentToUse.recipient ||
                   contentToUse.to ||
                   contentToUse.email ||
                   contentToUse.emailAddress ||
                   contentToUse.email_address ||
                   contentToUse.recipients ||
                   contentToUse.mail_to;
          Logger.debug('fill', `Email recipient field matching: ${field.name}`, { content, availableKeys: Object.keys(contentToUse) });
        } else if (field.name === 'subject') {
          // Enhanced email subject matching
          content = contentToUse.subject ||
                   contentToUse.title ||
                   contentToUse.topic ||
                   contentToUse.heading ||
                   contentToUse.summary;
          Logger.debug('fill', `Email subject field matching: ${field.name}`, { content, availableKeys: Object.keys(contentToUse) });
        } else if (field.name === 'body') {
          // Enhanced email body matching
          content = contentToUse.body ||
                   contentToUse.content ||
                   contentToUse.message ||
                   contentToUse.text ||
                   contentToUse.email_body ||
                   contentToUse.email_content ||
                   contentToUse.description;
          Logger.debug('fill', `Email body field matching: ${field.name}`, { content, availableKeys: Object.keys(contentToUse) });
          // Add email client specific handling for recipient fields
          if (content) {
            // 检查是否是 Gmail 或 Outlook 的收件人字段
            const isGmailRecipient = field.element.classList.contains('agP') ||
                                  field.element.classList.contains('aFw') ||
                                  (field.element.getAttribute('role') === 'textbox' &&
                                   (field.element.getAttribute('aria-label')?.includes('To') ||
                                    field.element.getAttribute('aria-label')?.includes('收件人')));

            const isOutlookRecipient = field.element.classList.contains('EditorClass') ||
                                    (field.element.getAttribute('role') === 'textbox' &&
                                     (field.element.getAttribute('aria-label')?.includes('To') ||
                                      field.element.getAttribute('aria-label')?.includes('收件人')));

            // 记录特殊字段类型以便后续处理
            if (isGmailRecipient) {
              field.type = 'gmail-recipient';
            } else if (isOutlookRecipient) {
              field.type = 'outlook-recipient';
            }
          }
        } else if (field.name === 'title' || field.name === 'description') {
          // 增强 GitHub 字段匹配策略
          const possibleTitleKeys = ['title', ':r1:', ':r21:', 'issue_title', 'issue[title]', 'name', 'summary', 'subject'];
          const possibleDescKeys = ['description', ':r6:', ':r26:', 'issue_body', 'issue[body]', 'body', 'content', 'details', 'comment', 'message'];

          // Debug: Log available content keys
          Logger.debug('fill', `Available content keys for ${field.name}:`, Object.keys(contentToUse));

          if (field.name === 'title') {
            // 尝试所有可能的标题键
            for (const key of possibleTitleKeys) {
              if (contentToUse[key]) {
                content = contentToUse[key];
                Logger.debug('fill', `Found title content using key: ${key}`);
                break;
              }
            }

            // 如果没有找到内容，尝试直接使用第一个键值对
            if (!content && Object.keys(contentToUse).length > 0) {
              const firstKey = Object.keys(contentToUse)[0];
              if (typeof contentToUse[firstKey] === 'string' &&
                  contentToUse[firstKey].length < 100) { // 标题通常较短
                content = contentToUse[firstKey];
                Logger.debug('fill', `Using first content key as title: ${firstKey}`);
              }
            }
          } else if (field.name === 'description') {
            // 尝试所有可能的描述键
            for (const key of possibleDescKeys) {
              if (contentToUse[key]) {
                content = contentToUse[key];
                Logger.debug('fill', `Found description content using key: ${key}`);
                break;
              }
            }

            // 如果没有找到内容，尝试使用最长的字符串值
            if (!content) {
              let longestContent = '';
              for (const [key, value] of Object.entries(contentToUse)) {
                if (typeof value === 'string' && value.length > longestContent.length && value.length > 100) {
                  longestContent = value;
                  Logger.debug('fill', `Found longer content for description using key: ${key}`);
                }
              }
              if (longestContent) {
                content = longestContent;
              }
            }

            // 如果仍然没有找到内容，尝试使用整个内容对象
            if (!content && typeof contentToUse === 'object') {
              try {
                // 将整个内容对象转换为格式化的文本
                const formattedContent = Object.entries(contentToUse)
                  .filter(([key, value]) => typeof value === 'string' && value.trim() !== '')
                  .map(([key, value]) => `${key}: ${value}`)
                  .join('\n\n');

                if (formattedContent) {
                  content = formattedContent;
                  Logger.debug('fill', 'Using formatted content object as description');
                }
              } catch (e) {
                Logger.error('Error formatting content object:', e);
              }
            }
          }

          Logger.debug('fill', `Matching GitHub field: ${field.name}`, {
            fieldFound: !!field.element,
            contentFound: !!content,
            elementType: field.element?.tagName,
            elementClasses: field.element?.className,
            elementId: field.element?.id,
            elementName: field.element?.getAttribute('name'),
            elementRole: field.element?.getAttribute('role'),
            contentPreview: content?.substring(0, 50),
            contentKeys: Object.keys(contentToUse),
            fieldType: field.name,
            selectedContent: content
          });
        } else {
          // 更通用的内容匹配逻辑
          const labelNoAsterisk = field.label?.replace(/\s*\*\s*$/, '') || ''
          const labelFirstWord = field.label?.split(/[\s*]/)[0] || ''

          // 创建一个根据字段语言特性进行匹配的函数
          const findValueByLanguage = (obj: any, key: string): string | undefined => {
            if (!key) return undefined

            // 直接匹配
            if (obj[key] !== undefined) return obj[key]

            // 不区分大小写匹配
            const lowerKey = key.toLowerCase()
            const keyMatch = Object.keys(obj).find(k => k.toLowerCase() === lowerKey)
            if (keyMatch) return obj[keyMatch]

            // 部分匹配
            const partialMatch = Object.keys(obj).find(k =>
              k.toLowerCase().includes(lowerKey) || lowerKey.includes(k.toLowerCase())
            )
            if (partialMatch) return obj[partialMatch]

            // 尝试智能匹配常见字段
            const commonFieldMappings: Record<string, string[]> = {
              'name': ['name', 'fullname', 'full_name', 'full-name', 'username', 'user', 'firstname', 'first_name', 'first-name'],
              'email': ['email', 'email_address', 'email-address', 'mail'],
              'phone': ['phone', 'telephone', 'tel', 'mobile', 'cell', 'phone_number', 'phone-number'],
              'address': ['address', 'street', 'location'],
              'city': ['city', 'town'],
              'state': ['state', 'province', 'region'],
              'country': ['country', 'nation'],
              'zip': ['zip', 'zipcode', 'postal', 'postal_code', 'postal-code'],
              'company': ['company', 'organization', 'organisation', 'employer'],
              'website': ['website', 'site', 'url', 'web', 'homepage'],
              'message': ['message', 'comment', 'feedback', 'note', 'description', 'details'],
              // Email specific mappings
              'recipient': ['recipient', 'to', 'recipients', 'email', 'email_address', 'mail_to'],
              'subject': ['subject', 'title', 'topic', 'heading', 'summary'],
              'body': ['body', 'content', 'message', 'text', 'email_body', 'email_content', 'description']
            }

            for (const [category, synonyms] of Object.entries(commonFieldMappings)) {
              if (synonyms.some(s => lowerKey.includes(s) || s.includes(lowerKey))) {
                const categoryMatch = Object.keys(obj).find(k =>
                  commonFieldMappings[category]?.some(s => k.toLowerCase().includes(s))
                )
                if (categoryMatch) return obj[categoryMatch]
              }
            }

            return undefined
          }

          // 尝试多种匹配方式
          content = findValueByLanguage(contentToUse, field.name) ||
                   findValueByLanguage(contentToUse, field.id) ||
                   findValueByLanguage(contentToUse, labelNoAsterisk) ||
                   findValueByLanguage(contentToUse, labelFirstWord) ||
                   findValueByLanguage(contentToUse, field.placeholder || '')

          Logger.debug('fill', `Matching field: ${field.name}`, {
            label: field.label,
            content,
            matchAttempts: [field.name, field.id, labelNoAsterisk, labelFirstWord, field.placeholder]
          })
        }

        if (content) {
          fillFieldContent(field, content)
        } else {
          // 如果没有找到匹配的内容，只移除加载动画
        }
      } catch (e) {
        Logger.error(`Error filling field ${field.name}:`, e)
      }
    })

    // 回调处理
    if (callback) {
      callback.success?.()
    }
  } catch (error) {
    Logger.error('Error filling form:', error)

    // 回调处理
    if (callback) {
      callback.error?.(error)
    }
  }
}

// 更新 fillFieldContent 函数
function fillFieldContent(field: FormField, content: string): void {
  if (!field.element || !content) {
    Logger.debug('fill', `Skipping field fill due to missing element or content`, {
      hasElement: !!field.element,
      hasContent: !!content,
      fieldName: field.name
    });
    return;
  }

  // 特殊处理 GitHub 字段
  if (window.location.hostname.includes('github.com')) {
    Logger.debug('fill', `Processing GitHub field: ${field.name}`, {
      elementType: field.element.tagName,
      elementId: field.element.id,
      elementClasses: field.element.className,
      ariaLabel: field.element.getAttribute('aria-label'),
      dataResize: field.element.getAttribute('data-resize'),
      placeholder: field.element.getAttribute('placeholder')
    });

    try {
      // 通用处理 GitHub 元素的方法
      if (field.name === 'description' || field.name === 'body') {
        // 处理描述字段
        if (field.element.tagName.toLowerCase() === 'textarea') {
          Logger.debug('fill', 'Filling GitHub textarea element');

          // 尝试使用 React 特殊处理方式
          const nativeValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLTextAreaElement.prototype,
            "value"
          )?.set;

          if (nativeValueSetter) {
            // 聚焦元素
            field.element.focus();

            // 使用原生 setter 设置值
            nativeValueSetter.call(field.element, content);

            // 触发一系列事件来确保 React 状态更新
            ['input', 'change', 'blur'].forEach(eventType => {
              const event = new Event(eventType, {
                bubbles: true,
                composed: true,
                cancelable: true
              });
              field.element.dispatchEvent(event);
            });

            // 创建并触发输入事件
            const inputEvent = new InputEvent('input', {
              bubbles: true,
              cancelable: true,
              inputType: 'insertText',
              data: content
            });
            field.element.dispatchEvent(inputEvent);

            // 最后失去焦点
            field.element.blur();

            Logger.debug('fill', 'Filled GitHub textarea using React method');
            return;
          }
        } else if (field.element.getAttribute('role') === 'textbox' || field.element.getAttribute('contenteditable') === 'true') {
          // 处理 role="textbox" 或 contenteditable 元素
          Logger.debug('fill', 'Filling GitHub textbox/contenteditable element');

          // 聚焦元素
          field.element.focus();

          // 清空现有内容
          field.element.innerHTML = '';

          // 处理换行符，将\n转换为<br>标签
          const formattedContent = content.replace(/\n/g, '<br>');
          field.element.innerHTML = formattedContent;

          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, composed: true });
            field.element.dispatchEvent(event);
          });

          Logger.debug('fill', 'Filled GitHub textbox/contenteditable element');
          return;
        }
      } else if (field.name === 'title') {
        // 处理标题字段
        if (field.element.tagName.toLowerCase() === 'input') {
          Logger.debug('fill', 'Filling GitHub title input element');

          // 尝试使用 React 特殊处理方式
          const nativeValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLInputElement.prototype,
            "value"
          )?.set;

          if (nativeValueSetter) {
            // 聚焦元素
            field.element.focus();

            // 使用原生 setter 设置值
            nativeValueSetter.call(field.element, content);

            // 触发一系列事件来确保 React 状态更新
            ['input', 'change', 'blur'].forEach(eventType => {
              const event = new Event(eventType, {
                bubbles: true,
                composed: true,
                cancelable: true
              });
              field.element.dispatchEvent(event);
            });

            // 创建并触发输入事件
            const inputEvent = new InputEvent('input', {
              bubbles: true,
              cancelable: true,
              inputType: 'insertText',
              data: content
            });
            field.element.dispatchEvent(inputEvent);

            // 最后失去焦点
            field.element.blur();

            Logger.debug('fill', 'Filled GitHub title input using React method');
            return;
          }
        }
      }
    } catch (e) {
      Logger.error('Error filling GitHub element:', e);
    }
  }

  // 特殊处理 Gmail 和 Outlook 的收件人字段
  if (field.type === 'gmail-recipient' || field.type === 'outlook-recipient') {
    try {
      // 聚焦元素
      field.element.focus();

      // 清空现有内容
      if (field.element.tagName === 'INPUT') {
        (field.element as HTMLInputElement).value = content;
      } else if (field.element.getAttribute('contenteditable') === 'true') {
        field.element.innerHTML = '';
        const textNode = document.createTextNode(content);
        field.element.appendChild(textNode);
      } else {
        // 尝试设置值
        (field.element as any).value = content;
      }

      // 触发必要的事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true, composed: true });
        field.element.dispatchEvent(event);
      });

      // 模拟按下回车键来确认收件人
      setTimeout(() => {
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        });
        field.element.dispatchEvent(enterEvent);

        // 失去焦点
        setTimeout(() => {
          field.element.blur();
        }, 100);
      }, 100);

      Logger.debug('fill', `Filled ${field.type} field`);
      return;
    } catch (e) {
      Logger.error(`Error filling ${field.type} field:`, e);
    }
  }

  try {
    Logger.debug('fill', `Preparing to fill field: ${field.name}`, {
      content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      fullContent: content, // 添加完整内容输出
      elementDetails: {
        tagName: field.element.tagName,
        type: field.element instanceof HTMLInputElement ? field.element.type : 'not-input',
        classes: field.element.className,
        id: field.element.id,
        name: field.name,
        isVisible: isVisibleElement(field.element),
        attributes: Array.from(field.element.attributes).map(attr => ({
          name: attr.name,
          value: attr.value
        }))
      }
    });

    // 在填充之前记录字段的当前值
    const previousValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent;

    Logger.debug('fill', `Current field state before filling:`, {
      fieldName: field.name,
      previousValue
    });

    // 检查是否是 React 组件
    const isReactComponent = field.element.classList.contains('fui-Input__input') ||
                             field.element.hasAttribute('data-reactid') ||
                             !!field.element.closest('[data-reactroot]');

    // 检查是否是富文本编辑器
    const isRichTextEditor = field.element.getAttribute('contenteditable') === 'true' ||
                             field.element.classList.contains('ck-editor__editable') ||
                             field.element.classList.contains('tox-edit-area__iframe') ||
                             field.element.classList.contains('mce-content-body');

    // 检查是否是 iframe 编辑器
    const isIframeEditor = field.element.tagName === 'IFRAME' &&
                          (field.element.classList.contains('cke_wysiwyg_frame') ||
                           field.element.classList.contains('tox-edit-area__iframe'));

    // 处理 iframe 编辑器
    if (isIframeEditor) {
      try {
        const iframe = field.element as HTMLIFrameElement;
        const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDocument && iframeDocument.body) {
          iframeDocument.body.innerHTML = content;

          // 触发 iframe 内容变化事件
          const event = new Event('input', { bubbles: true });
          iframeDocument.body.dispatchEvent(event);

          Logger.debug('fill', 'Filled iframe editor content');
        }
      } catch (e) {
        Logger.error('Error filling iframe editor:', e);
      }
      return;
    }

    // 处理富文本编辑器
    if (isRichTextEditor) {
      // 检查是否是 Gmail 的内容编辑器
      const isGmailEditor = field.element.classList.contains('editable') ||
                          field.element.classList.contains('LW-avf') ||
                          field.element.closest('.Am.Al.editable') !== null ||
                          (field.element.getAttribute('role') === 'textbox' &&
                           field.element.closest('[aria-label="Message Body"]') !== null);

      // 检查是否是 Outlook 的内容编辑器
      const isOutlookEditor = field.element.getAttribute('role') === 'textbox' &&
                            (field.element.classList.contains('DziEn') ||
                             field.element.hasAttribute('aria-label') &&
                             (field.element.getAttribute('aria-label')?.includes('Message body') ||
                              field.element.getAttribute('aria-label')?.includes('正文')));

      if (isGmailEditor) {
        try {
          // 聚焦元素
          field.element.focus();

          // 清空现有内容
          field.element.innerHTML = '';

          // 处理换行符，将\n转换为<br>标签
          if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
            // 将换行符转换为HTML标签
            const formattedContent = content.replace(/\n/g, '<br>');
            field.element.innerHTML = formattedContent;
          } else {
            // 其他字段使用原始文本
            const textNode = document.createTextNode(content);
            field.element.appendChild(textNode);
          }

          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, composed: true });
            field.element.dispatchEvent(event);
          });

          Logger.debug('fill', 'Filled Gmail contenteditable element');
          return;
        } catch (e) {
          Logger.error('Failed to set Gmail contenteditable element value:', e);
        }
      }

      if (isOutlookEditor) {
        try {
          // 聚焦元素
          field.element.focus();

          // 清空现有内容
          field.element.innerHTML = '';

          // 处理换行符，将\n转换为<br>标签
          if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
            // 将换行符转换为HTML标签
            const formattedContent = content.replace(/\n/g, '<br>');
            field.element.innerHTML = formattedContent;
          } else {
            // 其他字段使用原始文本
            const textNode = document.createTextNode(content);
            field.element.appendChild(textNode);
          }

          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, composed: true });
            field.element.dispatchEvent(event);
          });

          // 如果是收件人字段，额外处理
          if (field.name === 'to' || field.name === 'recipient') {
            // 模拟按下回车键
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              keyCode: 13,
              which: 13,
              bubbles: true,
              cancelable: true
            });
            field.element.dispatchEvent(enterEvent);
          }

          Logger.debug('fill', 'Filled Outlook contenteditable element');
          return;
        } catch (e) {
          Logger.error('Failed to set Outlook contenteditable element value:', e);
        }
      }

      // 如果不是特定的邮件客户端，使用通用方法
      // 处理换行符，将\n转换为<br>标签
      if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
        // 将换行符转换为HTML标签
        const formattedContent = content.replace(/\n/g, '<br>');
        field.element.innerHTML = formattedContent;
      } else {
        field.element.innerHTML = content;
      }

      // 触发内容变化事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        field.element.dispatchEvent(event);
      });

      Logger.debug('fill', 'Filled rich text editor content');
      return;
    }

    // 处理 React 组件
    if (isReactComponent) {
      if (field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement) {
        const nativeValueSetter = Object.getOwnPropertyDescriptor(
          field.element instanceof HTMLInputElement ? window.HTMLInputElement.prototype : window.HTMLTextAreaElement.prototype,
          "value"
        )?.set;

        if (nativeValueSetter) {
          nativeValueSetter.call(field.element, content);

          // 触发一系列事件来确保 React 状态更新
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, {
              bubbles: true,
              composed: true,
              cancelable: true
            });
            field.element.dispatchEvent(event);
          });

          // 确保元素获得焦点
          field.element.focus();

          // 创建并触发输入事件
          const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: content
          });
          field.element.dispatchEvent(inputEvent);

          // 最后失去焦点
          field.element.blur();

          Logger.debug('fill', 'Filled React input component');
        }
      } else if (field.element instanceof HTMLSelectElement) {
        // 处理 React select 组件
        fillSelectElement(field.element, content);
      }
      return;
    }

    // 处理普通 HTML 元素
    if (field.element instanceof HTMLInputElement) {
      const input = field.element;

      // 根据输入类型进行处理
      switch (input.type.toLowerCase()) {
        case 'checkbox':
          // 根据内容决定是否选中
          const shouldCheck = /^(yes|true|1|on|checked|selected|enable|enabled)$/i.test(content.trim());
          input.checked = shouldCheck;
          break;

        case 'radio':
          // 只有当内容与值匹配时才选中
          if (input.value.toLowerCase() === content.toLowerCase()) {
            input.checked = true;
          } else {
            // 尝试查找同名的其他单选按钮
            const radioGroup = document.querySelectorAll(`input[type="radio"][name="${input.name}"]`);
            for (const radio of Array.from(radioGroup)) {
              if ((radio as HTMLInputElement).value.toLowerCase() === content.toLowerCase()) {
                (radio as HTMLInputElement).checked = true;
                break;
              }
            }
          }
          break;

        case 'date':
          // 尝试解析日期
          try {
            const date = new Date(content);
            if (!isNaN(date.getTime())) {
              // 格式化为 YYYY-MM-DD
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              input.value = `${year}-${month}-${day}`;
            }
          } catch (e) {
            // 如果无法解析，使用原始内容
            input.value = content;
          }
          break;

        default:
          // 对于其他类型的输入框，直接设置值
          input.value = content;
      }

      // 触发事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

    } else if (field.element instanceof HTMLTextAreaElement) {
      const textarea = field.element;
      textarea.value = content;

      // 触发事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        textarea.dispatchEvent(event);
      });

    } else if (field.element instanceof HTMLSelectElement) {
      fillSelectElement(field.element, content);
    } else {
      // 对于其他类型的元素，尝试设置 textContent
      field.element.textContent = content;

      // 触发通用事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        field.element.dispatchEvent(event);
      });
    }

    // 在填充后记录字段的新值
    const newValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent;

    Logger.debug('fill', `Field state after filling:`, {
      fieldName: field.name,
      newValue,
      valueChanged: newValue !== previousValue
    });

    Logger.debug('fill', `Successfully filled field: ${field.name}`);
  } catch (error) {
    Logger.error(`Error filling field ${field.name}:`, error);
  }
}

// 辅助函数：填充 select 元素
function fillSelectElement(select: HTMLSelectElement, content: string): void {
  // 尝试多种方式匹配选项
  const contentLower = content.toLowerCase().trim();
  const options = Array.from(select.options);

  // 1. 尝试完全匹配值
  let matchedOption = options.find(option => option.value === content);

  // 2. 尝试完全匹配文本
  if (!matchedOption) {
    matchedOption = options.find(option => option.text === content);
  }

  // 3. 尝试不区分大小写匹配
  if (!matchedOption) {
    matchedOption = options.find(option =>
      option.value.toLowerCase() === contentLower ||
      option.text.toLowerCase() === contentLower
    );
  }

  // 4. 尝试部分匹配
  if (!matchedOption) {
    matchedOption = options.find(option =>
      option.value.toLowerCase().includes(contentLower) ||
      contentLower.includes(option.value.toLowerCase()) ||
      option.text.toLowerCase().includes(contentLower) ||
      contentLower.includes(option.text.toLowerCase())
    );
  }

  // 如果找到匹配项，设置选中
  if (matchedOption) {
    select.value = matchedOption.value;

    // 触发 change 事件
    const event = new Event('change', { bubbles: true });
    select.dispatchEvent(event);

    Logger.debug('fill', `Selected option: ${matchedOption.text} (${matchedOption.value})`);
  } else {
    Logger.debug('fill', `No matching option found for: ${content}`);
  }
}

// 处理未捕获的 Promise 错误
window.addEventListener('unhandledrejection', function(event) {
  if (errorMessages.some(msg => event.reason?.message?.includes(msg))) {
    event.preventDefault()
    event.stopPropagation()
  }
})

// 处理常规错误
window.addEventListener('error', function(event) {
  if (errorMessages.some(msg => event.error?.message?.includes(msg))) {
    event.preventDefault()
    event.stopPropagation()
  }
}, true)

// 更新 checkPageStatus 函数
function checkPageStatus(): {
  isValid: boolean;
  needsRefresh: boolean;
  hasFormFields: boolean;
  pageType?: string;
} {
  try {
    const isLoaded = document.readyState === 'complete';

    // 检查特定网站
    const isGmail = window.location.hostname.includes('mail.google.com');
    const isOutlook = window.location.hostname.includes('outlook.live.com') ||
                     window.location.hostname.includes('outlook.office.com');
    const isGitHub = window.location.hostname.includes('github.com');

    let pageType = 'unknown';
    if (isGmail) pageType = 'gmail';
    else if (isOutlook) pageType = 'outlook';
    else if (isGitHub) pageType = 'github';

    // 特定网站的加载检查
    let isLoading = false;

    if (isGmail) {
      // Gmail 特定检查
      const mainContent = document.querySelector('div[role="main"]');
      const composeButton = document.querySelector('[gh="cm"]'); // 撰写邮件按钮
      isLoading = !mainContent || !composeButton;
    } else if (isOutlook) {
      // Outlook 特定检查
      const mainContent = document.querySelector('div[role="main"]');
      const newMailButton = document.querySelector('[aria-label*="New mail"], [aria-label*="新邮件"]');
      isLoading = !mainContent || !newMailButton;
    } else if (isGitHub) {
      // GitHub 特定检查 - 增强检测逻辑
      isLoading = !!document.querySelector('.is-loading, .loading');

      // 检查是否在 issue 页面
      const isIssuePage = window.location.pathname.includes('/issues/') ||
                         window.location.pathname.includes('/issues/new');

      // 检查是否有表单元素
      const hasIssueForm = !!document.querySelector('form.js-new-issue-form, .js-issues-listing');

      // 检查是否有标题和描述字段
      const hasTitleField = !!document.querySelector('input[aria-label="Add a title"], input[aria-label="Title"], input[name="issue[title]"], input#issue_title');
      const hasDescField = !!document.querySelector('textarea[name="issue[body]"], textarea#issue_body, [data-testid="issue-body"], [role="textbox"][aria-label="Comment body"]');

      Logger.debug('form', 'GitHub page status check:', {
        isIssuePage,
        hasIssueForm,
        hasTitleField,
        hasDescField,
        isLoading
      });

      // 如果在 issue 页面但没有找到表单元素，可能是页面还在加载
      if (isIssuePage && !hasIssueForm && !hasTitleField && !hasDescField) {
        isLoading = true;
      }
    } else {
      // 通用检查
      isLoading = document.documentElement.classList.contains('loading') ||
                  document.body.classList.contains('loading');
    }

    // 检测表单字段
    const formFields = detectFormFields();
    const hasFields = formFields.length > 0;

    // 如果页面已加载完成且找到了表单字段，就不需要刷新
    const needsRefresh = !isLoaded || (isLoading && !hasFields);

    return {
      isValid: true,
      needsRefresh,
      hasFormFields: hasFields,
      pageType
    };
  } catch (error) {
    Logger.error('Error checking page status:', error);
    return {
      isValid: false,
      needsRefresh: true,
      hasFormFields: false
    };
  }
}

// 修改 setupMessageListener 函数
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    try {
      Logger.debug('message', 'Received message:', message);

      switch (message.action || message.type) {
        case 'fillForm':
          Logger.debug('fill', 'Received fillForm message with data:', message.data);
          // 确保我们传递正确的数据结构
          const formContentToFill = message.data.data || message.data;
          fillForm(formContentToFill, {
            success: () => {
              Logger.success('fill', 'Form filled successfully');
              sendResponse({ success: true });
            },
            error: (error) => {
              Logger.error('Failed to fill form:', error);
              sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
            }
          });
          return true;

        case 'showGeneratingEffect':
          Logger.debug('effect', 'Received showGeneratingEffect message');
          showGeneratingEffect();
          sendResponse({ success: true });
          return true;

        case 'checkFormFields':
          const formFields = detectFormFields();
          Logger.debug('form', 'Checking form fields, found:', formFields.length);
          sendResponse({
            hasFields: formFields.length > 0,
            fieldCount: formFields.length
          });
          return true;

        case 'checkPageStatus':
          const status = checkPageStatus();
          Logger.debug('form', 'Checking page status:', status);
          sendResponse(status);
          return true;

        case 'getFormFields':
          const fields = detectFormFields();
          Logger.debug('form', 'Getting form fields, found:', fields.length);
          sendResponse({
            success: true,
            fields: fields.map(field => ({
              type: field.type,
              id: field.id,
              name: field.name,
              placeholder: field.placeholder,
              label: field.label
            }))
          });
          return true;

        case 'detectFormFields':
          Logger.debug('form', 'Received detectFormFields message');
          try {
            const formFields = detectFormFields();
            sendResponse({
              success: true,
              fields: formFields.map(field => ({
                type: field.type,
                id: field.id,
                name: field.name,
                placeholder: field.placeholder,
                label: field.label
              }))
            });
          } catch (error) {
            Logger.error('Error detecting form fields:', error);
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : 'Failed to detect form fields'
            });
          }
          return true;

        case 'openInPagePopup':
          Logger.debug('popup', 'Received openInPagePopup message from context menu');
          showInPagePopup().then(success => {
            sendResponse({ success });
          }).catch(error => {
            Logger.error('Failed to show in-page popup:', error);
            sendResponse({ success: false, error: error.message });
          });
          return true;

        default:
          Logger.error('Unknown message type:', JSON.stringify(message));
          sendResponse({ success: false, error: 'Unknown message type' });
          return true;
      }
    } catch (error) {
      Logger.error('Error handling message:', error);
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      return true;
    }
  });

  // 发送初始化成功消息
  chrome.runtime.sendMessage({
    type: 'contentScriptInitialized',
    url: window.location.href
  }).catch(error => {
    // 忽略连接错误，这在某些情况下是正常的
    if (!error.message.includes('Receiving end does not exist') && 
        !error.message.includes('Could not establish connection')) {
      Logger.error('Error sending initialization message:', error);
    }
  });

  // 设置 MutationObserver 来监听 DOM 变化
  setupDOMObserver();
}

// 设置 DOM 变化观察器
function setupDOMObserver() {
  // 只在 GitHub 页面上设置观察器
  if (!window.location.hostname.includes('github.com')) {
    return;
  }

  Logger.debug('form', 'Setting up DOM mutation observer for GitHub');

  // 创建一个观察器实例
  const observer = new MutationObserver((mutations) => {
    // 检查是否有相关变化
    const relevantChanges = mutations.some(mutation => {
      // 检查是否添加了新节点
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // 检查添加的节点是否包含表单元素
        return Array.from(mutation.addedNodes).some(node => {
          if (node.nodeType !== Node.ELEMENT_NODE) return false;

          const element = node as Element;

          // 检查是否是表单元素或包含表单元素
          return element.tagName === 'FORM' ||
                 element.tagName === 'INPUT' ||
                 element.tagName === 'TEXTAREA' ||
                 element.querySelector('input, textarea, [role="textbox"]') !== null;
        });
      }

      // 检查属性变化
      if (mutation.type === 'attributes') {
        const element = mutation.target as Element;

        // 检查是否是表单元素的相关属性
        return element.tagName === 'INPUT' ||
               element.tagName === 'TEXTAREA' ||
               element.getAttribute('role') === 'textbox' ||
               mutation.attributeName === 'contenteditable';
      }

      return false;
    });

    // 如果有相关变化，重新检测表单字段
    if (relevantChanges) {
      Logger.debug('form', 'Detected relevant DOM changes, re-detecting form fields');

      // 使用 setTimeout 来确保 DOM 已经完全更新
      setTimeout(() => {
        const formFields = detectFormFields();
        Logger.debug('form', 'Re-detected form fields after DOM changes:', formFields.length);
      }, 500);
    }
  });

  // 配置观察选项
  const config = {
    childList: true,     // 观察子节点的添加或删除
    subtree: true,       // 观察所有后代节点
    attributes: true,    // 观察属性变化
    attributeFilter: ['role', 'contenteditable', 'class', 'id', 'aria-label'] // 只观察这些属性的变化
  };

  // 开始观察整个文档
  observer.observe(document.documentElement, config);

  Logger.debug('form', 'DOM mutation observer setup complete');
}

// 注入式popup相关变量
let inPagePopup: HTMLElement | null = null;
let popupOverlay: HTMLElement | null = null;
let isPopupVisible = false;

// 拖拽相关变量
let isDragging = false;
let dragStartX = 0;
let dragStartY = 0;
let popupStartX = 0;
let popupStartY = 0;
let longPressTimer: number | null = null;
let isLongPressTriggered = false;

// 设置注入式popup
function setupInPagePopup() {
  // 监听快捷键 (Ctrl/Cmd + Shift + F)
  document.addEventListener('keydown', (event) => {
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F') {
      event.preventDefault();
      toggleInPagePopup();
    }
  });
}



// 切换注入式popup显示状态
function toggleInPagePopup() {
  if (isPopupVisible) {
    hideInPagePopup();
  } else {
    showInPagePopup();
  }
}

// 显示注入式popup
async function showInPagePopup(): Promise<boolean> {
  if (isPopupVisible) return true;

  try {
    // 首先检查页面状态
    const pageStatus = checkPageStatus();

    // 如果页面状态不佳，通知background script打开Chrome popup
    if (!pageStatus.isValid || (pageStatus.needsRefresh && !pageStatus.hasFormFields)) {
      Logger.debug('popup', 'Page status invalid, notifying background to open Chrome popup');

      // 通知background script页面状态不佳，需要打开Chrome popup
      chrome.runtime.sendMessage({
        type: 'openChromePopupForError',
        reason: 'page_invalid',
        pageStatus
      }).catch(error => {
        Logger.error('Failed to notify background about page error:', error);
      });

      return false;
    }

    // 页面状态正常，创建注入popup
    await createInPagePopup();

    isPopupVisible = true;
    Logger.debug('popup', 'In-page popup shown');
    return true;
  } catch (error) {
    Logger.error('Error showing in-page popup:', error);

    // 创建popup失败，通知background script打开Chrome popup
    chrome.runtime.sendMessage({
      type: 'openChromePopupForError',
      reason: 'popup_creation_failed',
      error: error.message
    }).catch(bgError => {
      Logger.error('Failed to notify background about popup creation error:', bgError);
    });

    return false;
  }
}

// 隐藏注入式popup
function hideInPagePopup() {
  if (!isPopupVisible) return;

  // 添加退出动画然后移除popup
  if (inPagePopup) {
    inPagePopup.style.animation = 'slideOutToBottom 0.3s ease-in forwards';
    setTimeout(() => {
      if (inPagePopup) {
        inPagePopup.remove();
        inPagePopup = null;
      }
    }, 300);
  }

  isPopupVisible = false;
  Logger.debug('popup', 'In-page popup hidden');
}



// 创建注入式popup
async function createInPagePopup() {
  inPagePopup = document.createElement('div');
  inPagePopup.id = 'fillify-in-page-popup';

  // 获取登录状态和用户信息
  const loginStatus = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
  const userInfo = loginStatus.isLoggedIn ? await chrome.runtime.sendMessage({ type: 'getUserInfo' }) : null;

  // 设置popup样式 - 定位到右下角
  inPagePopup.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 380px;
    min-height: 370px;
    background: #f5f5f5;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
    z-index: 10001;
    animation: slideInFromBottom 0.3s ease-out;
  `;

  // 添加动画样式
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideInFromBottom {
      from {
        transform: translateY(100%);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @keyframes slideOutToBottom {
      from {
        transform: translateY(0);
        opacity: 1;
      }
      to {
        transform: translateY(100%);
        opacity: 0;
      }
    }

    /* Logo animations matching Chrome popup */
    .fillify-login-logo path[d*="M245.625"] {
      transform-box: fill-box;
      transform-origin: center;
      animation: starPulse 2s ease-in-out infinite;
    }

    .fillify-login-logo path[d*="M245.625"]:nth-of-type(1) {
      animation-delay: 0s;
    }

    .fillify-login-logo path[d*="M245.625"]:nth-of-type(2) {
      animation-delay: -0.6s;
    }

    .fillify-login-logo path[d*="M245.625"]:nth-of-type(3) {
      animation-delay: -1.2s;
    }

    .fillify-login-logo rect[x="340.211"] {
      transform-box: fill-box;
      transform-origin: left;
      animation: rectStretch 3s ease-in-out infinite;
    }

    .fillify-login-logo rect[x="340.211"]:nth-of-type(1) {
      animation-delay: 0s;
    }

    .fillify-login-logo rect[x="340.211"]:nth-of-type(2) {
      animation-delay: -1s;
    }

    .fillify-login-logo rect[x="340.211"]:nth-of-type(3) {
      animation-delay: -2s;
    }

    @keyframes starPulse {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.15);
      }
    }

    @keyframes rectStretch {
      0%, 100% {
        transform: scaleX(1);
      }
      50% {
        transform: scaleX(1.1);
      }
    }

    /* Button hover effects */
    .fillify-sign-in-btn:hover {
      background: #1850D8 !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(29, 93, 244, 0.2);
    }

    .fillify-sign-in-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(29, 93, 244, 0.2);
    }

    .fillify-skip-login-text:hover {
      color: #1D5DF4 !important;
      text-decoration: underline;
    }

    /* Main popup styles matching Chrome popup */
    .fillify-settings-button:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }

    .fillify-avatar-menu:hover .fillify-menu-popover {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(0);
      pointer-events: auto;
    }

    .fillify-menu-popover:hover {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(0);
      pointer-events: auto;
    }

    .fillify-menu-item:hover {
      background-color: #f5f5f5;
    }

    /* Mode buttons styling */
    .fillify-mode-buttons::after {
      content: '';
      position: absolute;
      top: 4px;
      left: 4px;
      width: calc((100% - 16px) / 3);
      height: calc(100% - 8px);
      background: white;
      border-radius: 6px;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      z-index: 0;
      transform: translateX(0);
    }

    .fillify-mode-btn:active {
      transform: scale(0.97);
    }

    .fillify-mode-btn:hover {
      background: rgba(41, 98, 255, 0.1) !important;
    }

    .fillify-mode-btn.active:hover {
      background: transparent !important;
    }

    /* Select styling */
    select:focus {
      outline: none;
      border-color: #2962FF;
    }

    .fillify-add-project-btn:hover {
      background: #f5f5f5 !important;
      border-color: #ccc !important;
    }

    /* Textarea styling */
    #fillify-description:focus {
      outline: none;
      border-color: #2962FF !important;
    }

    /* Primary button styling */
    .fillify-primary-btn:hover:not(.success):not(.loading):not(:disabled) {
      background: #1E4EE3 !important;
    }

    .fillify-primary-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    .fillify-primary-btn.loading {
      background: #1E4EE3 !important;
      cursor: not-allowed;
    }

    .fillify-primary-btn.success {
      background: #2962FF !important;
      cursor: default;
      color: white;
    }

    /* Sparkle animation */
    @keyframes sparkle {
      0%, 100% {
        transform: scale(1);
        opacity: 0.9;
      }
      50% {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    .fillify-sparkle-icon.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .fillify-primary-btn:hover .fillify-sparkle-icon {
      animation: sparkle-hover 1s ease-in-out infinite;
    }

    @keyframes sparkle-hover {
      0%, 100% {
        transform: scale(1.1) rotate(0deg);
        opacity: 1;
      }
      50% {
        transform: scale(1.2) rotate(10deg);
        opacity: 0.9;
      }
    }

    /* Loading animation */
    .fillify-primary-btn .fillify-animation {
      display: none;
      position: absolute;
      border-radius: 100%;
      animation: ripple 0.6s linear infinite;
    }

    .fillify-primary-btn.loading .fillify-animation {
      display: block;
    }

    @keyframes ripple {
      0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
                   0 0 0 40px rgba(255, 255, 255, 0.1),
                   0 0 0 80px rgba(255, 255, 255, 0.1),
                   0 0 0 120px rgba(255, 255, 255, 0.1);
      }
      100% {
        box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
                   0 0 0 80px rgba(255, 255, 255, 0.1),
                   0 0 0 120px rgba(255, 255, 255, 0.1),
                   0 0 0 160px rgba(255, 255, 255, 0);
      }
    }

    /* Status message styling */
    .fillify-status-message.show {
      opacity: 1;
      transform: translateY(0);
    }

    .fillify-status-message.error {
      background: rgba(244, 67, 54, 0.9) !important;
    }

    .fillify-status-message.success {
      background: rgba(76, 175, 80, 0.9) !important;
    }

    .fillify-status-message.info {
      background: rgba(33, 150, 243, 0.9) !important;
    }

    /* Close button hover */
    #fillify-close-btn:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    /* Disabled states */
    textarea:disabled {
      background-color: #f5f5f5 !important;
      color: #999 !important;
      cursor: not-allowed !important;
      opacity: 0.6 !important;
    }

    .fillify-project-selector select:disabled {
      background-color: #f5f5f5 !important;
      color: #999 !important;
      cursor: not-allowed !important;
    }
  `;
  document.head.appendChild(style);

  // 创建popup内容
  const popupContent = await createPopupContent(loginStatus, userInfo);
  inPagePopup.innerHTML = popupContent;

  // 直接添加到body
  document.body.appendChild(inPagePopup);

  // 绑定事件处理器
  bindPopupEvents();
}

// 创建popup内容HTML
async function createPopupContent(loginStatus: any, userInfo: any): Promise<string> {
  const isLoggedIn = loginStatus?.isLoggedIn || false;
  const skipLogin = loginStatus?.skipLogin || false;
  const showLoginPrompt = !isLoggedIn && !skipLogin;

  if (showLoginPrompt) {
    return createLoginPromptContent();
  } else {
    return await createMainPopupContent(isLoggedIn, userInfo);
  }
}

// 创建登录提示内容
function createLoginPromptContent(): string {
  return `
    <div class="fillify-login-prompt" style="
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.98);
      min-height: 370px;
    ">
      <div class="fillify-login-content" style="
        text-align: center;
        padding: 30px 15px;
        border-radius: 12px;
        background: white;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 320px;
      ">
        <div class="fillify-logo-container" style="margin-bottom: 1rem;">
          <svg class="fillify-login-logo" width="120" height="120" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg" style="
            width: 120px;
            height: 120px;
            margin-bottom: 1rem;
          ">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M814.311 75H125C97.3858 75 75 97.3858 75 125V918C75 945.614 97.3858 968 125 968H905.481C933.096 968 955.481 945.614 955.481 918V216.17L814.311 75Z" fill="#1D5DF4"/>
            <g style="mix-blend-mode:hard-light">
              <path d="M956 217H814V75L885 146L956 217Z" fill="#D9D9D9"/>
            </g>
            <rect x="340.211" y="344.847" width="504.457" height="81.6033" fill="white"/>
            <rect x="340.211" y="508.054" width="504.457" height="81.6033" fill="white"/>
            <rect x="340.211" y="671.261" width="504.457" height="81.6033" fill="white"/>
            <path d="M245.625 333.72L260.152 372.977L299.409 387.504L260.152 402.03L245.625 441.288L231.099 402.03L191.841 387.504L231.099 372.977L245.625 333.72Z" fill="white"/>
            <path d="M245.625 496.926L260.152 536.184L299.409 550.71L260.152 565.237L245.625 604.494L231.099 565.237L191.841 550.71L231.099 536.184L245.625 496.926Z" fill="white"/>
            <path d="M245.625 660.133L260.152 699.39L299.409 713.917L260.152 728.443L245.625 767.701L231.099 728.443L191.841 713.917L231.099 699.39L245.625 660.133Z" fill="white"/>
          </svg>
        </div>
        <h2 style="
          color: #1D5DF4;
          font-size: 1.5rem;
          margin: 0 0 0.5rem;
          font-weight: 600;
        ">Welcome to Fillify</h2>
        <p style="
          color: #666;
          margin: 0 0 1.5rem;
          font-size: 0.9rem;
        ">Please sign in to access all features</p>
        <button id="fillify-signin-btn" class="fillify-sign-in-btn" style="
          background: #1D5DF4;
          color: white;
          border: none;
          padding: 0.8rem 2rem;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
          margin-bottom: 12px;
        ">Sign in</button>
        <a href="#" id="fillify-skip-login" class="fillify-skip-login-text" style="
          display: block;
          color: #666;
          font-size: 14px;
          text-decoration: none;
          margin-top: 12px;
          cursor: pointer;
          transition: color 0.2s ease;
        ">Skip sign in</a>
      </div>
    </div>
  `;
}

// 创建主popup内容
async function createMainPopupContent(isLoggedIn: boolean, userInfo: any): Promise<string> {
  // 获取存储的数据
  const storage = await chrome.storage.sync.get(['formify_last_mode', 'formify_projects', 'formify_last_language']);
  const currentMode = storage.formify_last_mode || 'general';
  const selectedLanguage = storage.formify_last_language || 'auto';
  const projects = storage.formify_projects || [];

  const userAvatarUrl = userInfo?.user?.picture_url || '';
  const userName = userInfo?.user?.name || '';

  // 检查表单字段
  const formFields = detectFormFields();
  const hasFormFields = formFields.length > 0;

  // 语言选项映射
  const languageMap: Record<string, string> = {
    'auto': 'Auto',
    'id': 'Bahasa Indonesia',
    'ms': 'Bahasa Melayu',
    'da': 'Dansk',
    'de': 'Deutsch',
    'en': 'English',
    'es': 'Español',
    'fr': 'Français',
    'it': 'Italiano',
    'nl': 'Nederlands',
    'no': 'Norsk',
    'pl': 'Polski',
    'pt': 'Português',
    'ro': 'Română',
    'fi': 'Suomi',
    'sv': 'Svenska',
    'vi': 'Tiếng Việt',
    'tr': 'Türkçe',
    'hu': 'Magyar',
    'cs': 'Čeština',
    'uk': 'Українська',
    'ru': 'Русский',
    'bg': 'Български',
    'ar': 'العربية',
    'fa': 'فارسی',
    'he': 'עִבְרִית',
    'hi': 'हिन्दी',
    'th': 'ไทย',
    'ja': '日本語',
    'zh-CN': '中文（简体）',
    'zh-TW': '中文（繁體）',
    'ko': '한국어'
  };

  const selectedLanguageText = languageMap[selectedLanguage] || selectedLanguage;

  // 获取占位符文本
  const getPlaceholder = (mode: string) => {
    switch (mode) {
      case 'bugReport': return 'Enter bug description';
      case 'email': return 'Enter email content description';
      default: return 'Enter description';
    }
  };

  return `
    <div class="fillify-container">
      <!-- 头部 -->
      <div class="fillify-header" style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 20px;
        background: #f5f5f5;
      ">
        <h1 style="margin: 0; color: #2962FF; font-size: 22px;">Fillify</h1>
        <div class="fillify-header-right" style="display: flex; align-items: center; gap: 10px;">
          <div class="fillify-login-status" style="
            display: flex;
            align-items: center;
            margin-right: 4px;
            font-size: 14px;
            color: #666;
          ">
            <div class="fillify-avatar-menu" style="position: relative; cursor: pointer;">
              ${isLoggedIn ? `
                <img src="${userAvatarUrl}" style="
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  cursor: pointer;
                " title="Go to Dashboard" id="fillify-avatar" />
                <div class="fillify-menu-popover" style="
                  position: absolute;
                  top: 100%;
                  left: 50%;
                  transform: translateX(-50%) translateY(-10px);
                  margin-top: 4px;
                  background: white;
                  border: 1px solid #eee;
                  border-radius: 8px;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                  z-index: 1000;
                  min-width: 100px;
                  opacity: 0;
                  visibility: hidden;
                  pointer-events: none;
                  transition: all 0.2s ease;
                  padding: 2px;
                ">
                  <div class="fillify-menu-item" style="
                    padding: 6px 12px;
                    font-size: 13px;
                    color: #dc2626;
                    transition: all 0.2s ease;
                    cursor: pointer;
                    margin: 2px;
                    border-radius: 4px;
                  " id="fillify-signout">Sign out</div>
                </div>
              ` : `
                <div style="cursor: pointer;" title="Sign in" id="fillify-signin-icon">
                  <svg width="24" height="24" viewBox="0 0 1024 1024" style="vertical-align: middle;">
                    <path d="M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64zM384.8 376c4-64 56-115.2 120-119.2 74.4-4 135.2 55.2 135.2 128 0 70.4-57.6 128-128 128-73.6 0-132-62.4-127.2-136.8zM768 746.4c0 12-9.6 21.6-21.6 21.6H278.4c-12 0-21.6-9.6-21.6-21.6v-64c0-84.8 170.4-128 255.2-128 84.8 0 255.2 42.4 255.2 128l0.8 64z" fill="#333333"/>
                  </svg>
                </div>
              `}
            </div>
          </div>
          <button class="fillify-settings-button" id="fillify-settings" style="
            cursor: pointer;
            position: relative;
            padding: 8px;
            background: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background 0.2s ease;
          " title="Settings">
            <div class="fillify-menu-icon" style="
              width: 18px;
              height: 14px;
              position: relative;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              pointer-events: none;
            ">
              <span style="
                display: block;
                width: 100%;
                height: 2px;
                background-color: #666;
                border-radius: 2px;
                transition: all 0.2s ease;
                pointer-events: none;
              "></span>
              <span style="
                display: block;
                width: 100%;
                height: 2px;
                background-color: #666;
                border-radius: 2px;
                transition: all 0.2s ease;
                pointer-events: none;
              "></span>
              <span style="
                display: block;
                width: 100%;
                height: 2px;
                background-color: #666;
                border-radius: 2px;
                transition: all 0.2s ease;
                pointer-events: none;
              "></span>
            </div>
          </button>
          <button id="fillify-close-btn" style="
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s ease;
          ">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="#666">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 表单配置区域 -->
      <form id="fillify-form-config" style="
        position: relative;
        background: white;
        border-radius: 16px;
        margin: 0 12px 12px 12px;
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      ">
        <!-- 模式选择器 -->
        <div class="fillify-mode-buttons" style="
          position: relative;
          display: flex;
          gap: 4px;
          padding: 4px;
          background: #f5f5f5;
          border-radius: 8px;
        ">
          <button type="button" class="fillify-mode-btn ${currentMode === 'general' ? 'active' : ''}" data-mode="general" style="
            flex: 1;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: ${currentMode === 'general' ? '#2962FF' : '#666'};
            font-size: 14px;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            user-select: none;
          ">General</button>
          <button type="button" class="fillify-mode-btn ${currentMode === 'email' ? 'active' : ''}" data-mode="email" style="
            flex: 1;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: ${currentMode === 'email' ? '#2962FF' : '#666'};
            font-size: 14px;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            user-select: none;
          ">Email</button>
          <button type="button" class="fillify-mode-btn ${currentMode === 'bugReport' ? 'active' : ''}" data-mode="bugReport" style="
            flex: 1;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: ${currentMode === 'bugReport' ? '#2962FF' : '#666'};
            font-size: 14px;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            user-select: none;
          ">Bug Report</button>
        </div>

        <!-- 项目选择器 (仅在Bug Report模式下显示) -->
        <div class="fillify-project-selector" style="
          margin: 0;
          display: ${currentMode === 'bugReport' ? 'block' : 'none'};
        ">
          <div class="fillify-select-wrapper" style="
            display: flex;
            gap: 10px;
            align-items: center;
          ">
            <select id="fillify-project-select" style="
              flex: 1;
              padding: 10px 14px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background: #fff;
              font-size: 14px;
              color: #333;
              cursor: pointer;
              appearance: none;
              background-image: url('data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'%23999\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpolyline points=\\'6 9 12 15 18 9\\'%3E%3C/polyline%3E%3C/svg%3E');
              background-repeat: no-repeat;
              background-position: right 12px center;
              background-size: 16px;
            " ${!hasFormFields ? 'disabled' : ''}>
              <option value="">Select any project (Optional)</option>
              ${projects.map((project: any) => `
                <option value="${project.id}">${project.name}</option>
              `).join('')}
            </select>
            <button type="button" class="fillify-add-project-btn" id="fillify-add-project" style="
              padding: 10px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background: #fff;
              color: #666;
              font-size: 16px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 42px;
              transition: all 0.2s ease;
            ">+</button>
          </div>
        </div>

        <!-- 描述输入区域 -->
        <div class="fillify-description-input" style="
          flex-grow: 1;
          display: flex;
          flex-direction: column;
          gap: 0;
        ">
          <div class="fillify-textarea-wrapper" style="
            width: 100%;
            margin-bottom: 2px;
          ">
            <textarea id="fillify-description" placeholder="${getPlaceholder(currentMode)}" style="
              width: 100%;
              padding: 14px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              line-height: 1.5;
              resize: none;
              transition: all 0.3s ease;
              height: ${currentMode === 'general' ? '200px' : currentMode === 'bugReport' ? '150px' : '150px'};
              box-sizing: border-box;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background-color: ${!hasFormFields ? '#f5f5f5' : '#fff'};
              color: ${!hasFormFields ? '#999' : '#333'};
              cursor: ${!hasFormFields ? 'not-allowed' : 'text'};
              opacity: ${!hasFormFields ? '0.6' : '1'};
            " ${!hasFormFields ? 'disabled' : ''}></textarea>
          </div>
          
          <!-- 语言选择器 -->
          <div class="fillify-language-selector" style="
            margin: 0;
            margin-bottom: -8px;
            display: flex;
            justify-content: flex-end;
          ">
            <div class="fillify-language-flex" style="
              position: relative;
              display: flex;
              align-items: center;
              gap: 2px;
              padding: 0 4px;
              height: 26px;
              border-radius: 4px;
              cursor: pointer;
            ">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px;">
                <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3.6001 9H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3.6001 15H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 21C13.6569 21 15 16.9706 15 12C15 7.02944 13.6569 3 12 3C10.3432 3 9 7.02944 9 12C9 16.9706 10.3432 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <div class="fillify-selected-language" style="
                color: #666;
                font-size: 12px;
                margin-right: 2px;
              ">${selectedLanguageText}</div>
              <select id="fillify-language-select" style="
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0;
                cursor: pointer;
              ">
                ${Object.entries(languageMap).map(([value, label]) => `
                  <option value="${value}" ${selectedLanguage === value ? 'selected' : ''}>${label}</option>
                `).join('')}
              </select>
            </div>
          </div>
        </div>

        <!-- 生成按钮 -->
        <button id="fillify-fill-button" type="submit" class="fillify-primary-btn" style="
          width: 100%;
          padding: 8px;
          border: none;
          border-radius: 8px;
          background: ${hasFormFields ? '#2962FF' : '#ccc'};
          color: white;
          font-size: 16px;
          font-weight: 500;
          cursor: ${hasFormFields ? 'pointer' : 'not-allowed'};
          position: relative;
          overflow: hidden;
          transition: all 0.3s ease;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-height: 40px;
          opacity: ${hasFormFields ? '1' : '0.7'};
        " ${!hasFormFields ? 'disabled' : ''}>
          <div class="fillify-button-content" style="
            display: flex;
            align-items: center;
            gap: 6px;
            position: relative;
            z-index: 1;
          ">
            <span class="fillify-button-text">Generate</span>
            <svg class="fillify-sparkle-icon" viewBox="0 0 24 24" fill="currentColor" style="
              width: 16px;
              height: 16px;
              color: inherit;
              opacity: 0.9;
              animation: sparkle 2s ease-in-out infinite;
              margin-left: 4px;
              transition: opacity 0.3s ease;
            ">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846a.75.75 0 011.442 0l.813 2.846a.75.75 0 001.442.384l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
            </svg>
          </div>
          <div class="fillify-animation" style="
            display: none;
            position: absolute;
            border-radius: 100%;
            animation: ripple 0.6s linear infinite;
          "></div>
        </button>
      </form>

      <!-- 状态消息容器 -->
      <div id="fillify-status-container" style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        pointer-events: none;
        z-index: 10002;
      ">
        <div id="fillify-status-message" class="fillify-status-message" style="
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 16px;
          border-radius: 4px;
          font-size: 14px;
          max-width: 80%;
          text-align: center;
          opacity: 0;
          transform: translateY(10px);
          transition: all 0.3s ease;
          pointer-events: none;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        "></div>
      </div>

      <!-- Confetti Canvas -->
      <canvas id="fillify-confetti-canvas" style="
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 1000;
        width: 100%;
        height: 100%;
      "></canvas>
    </div>
  `;
}

// 获取占位符文本
function getPlaceholderText(mode: string): string {
  switch (mode) {
    case 'bugReport': return 'Enter bug description';
    case 'email': return 'Enter email content description';
    default: return 'Enter description';
  }
}

// 绑定popup事件
function bindPopupEvents() {
  if (!inPagePopup) return;

  // 关闭按钮
  const closeBtn = inPagePopup.querySelector('#fillify-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', hideInPagePopup);
  }

  // 登录按钮
  const signinBtn = inPagePopup.querySelector('#fillify-signin-btn');
  if (signinBtn) {
    signinBtn.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      hideInPagePopup();
    });
  }

  // 跳过登录按钮
  const skipBtn = inPagePopup.querySelector('#fillify-skip-btn, #fillify-skip-login');
  if (skipBtn) {
    skipBtn.addEventListener('click', async (e) => {
      e.preventDefault();
      await chrome.runtime.sendMessage({ type: 'setSkipLogin', skip: true });
      // 重新创建popup内容
      const loginStatus = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
      const userInfo = loginStatus.isLoggedIn ? await chrome.runtime.sendMessage({ type: 'getUserInfo' }) : null;
      const newContent = await createPopupContent(loginStatus, userInfo);
      inPagePopup!.innerHTML = newContent;
      bindPopupEvents();
    });
  }

  // 头像点击
  const avatar = inPagePopup.querySelector('#fillify-avatar');
  if (avatar) {
    avatar.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openDashboard' });
      hideInPagePopup();
    });
  }

  // 登录图标点击
  const signinIcon = inPagePopup.querySelector('#fillify-signin-icon');
  if (signinIcon) {
    signinIcon.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      hideInPagePopup();
    });
  }

  // 退出登录
  const signoutBtn = inPagePopup.querySelector('#fillify-signout');
  if (signoutBtn) {
    signoutBtn.addEventListener('click', async () => {
      try {
        await chrome.runtime.sendMessage({ type: 'signOut' });
        // 重新创建popup内容
        const loginStatus = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
        const userInfo = null;
        const newContent = await createPopupContent(loginStatus, userInfo);
        inPagePopup!.innerHTML = newContent;
        bindPopupEvents();
      } catch (error) {
        showStatusMessage('Failed to sign out', 'error');
      }
    });
  }

  // 设置按钮
  const settingsBtn = inPagePopup.querySelector('#fillify-settings');
  if (settingsBtn) {
    settingsBtn.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openSettings' });
      hideInPagePopup();
    });
  }

  // 添加项目按钮
  const addProjectBtn = inPagePopup.querySelector('#fillify-add-project');
  if (addProjectBtn) {
    addProjectBtn.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openProjectSettings' });
      hideInPagePopup();
    });
  }

  // 模式切换按钮
  const modeButtons = inPagePopup.querySelectorAll('.fillify-mode-btn');
  modeButtons.forEach(btn => {
    btn.addEventListener('click', async (e) => {
      const target = e.target as HTMLElement;
      const mode = target.getAttribute('data-mode');
      if (mode) {
        // 保存模式到存储
        await chrome.storage.sync.set({ formify_last_mode: mode });

        // 重新创建popup内容以更新UI
        const loginStatus = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
        const userInfo = loginStatus.isLoggedIn ? await chrome.runtime.sendMessage({ type: 'getUserInfo' }) : null;
        const newContent = await createPopupContent(loginStatus, userInfo);
        inPagePopup!.innerHTML = newContent;
        bindPopupEvents();
        // 更新滑动背景位置
        updateModeBackground();
      }
    });
  });

  // 语言选择器
  const languageSelect = inPagePopup.querySelector('#fillify-language-select') as HTMLSelectElement;
  if (languageSelect) {
    languageSelect.addEventListener('change', async (e) => {
      const target = e.target as HTMLSelectElement;
      await chrome.storage.sync.set({ formify_last_language: target.value });
      
      // 更新显示的语言文本
      const selectedLanguageEl = inPagePopup?.querySelector('.fillify-selected-language');
      if (selectedLanguageEl) {
        const languageMap: Record<string, string> = {
          'auto': 'Auto',
          'id': 'Bahasa Indonesia',
          'ms': 'Bahasa Melayu',
          'da': 'Dansk',
          'de': 'Deutsch',
          'en': 'English',
          'es': 'Español',
          'fr': 'Français',
          'it': 'Italiano',
          'nl': 'Nederlands',
          'no': 'Norsk',
          'pl': 'Polski',
          'pt': 'Português',
          'ro': 'Română',
          'fi': 'Suomi',
          'sv': 'Svenska',
          'vi': 'Tiếng Việt',
          'tr': 'Türkçe',
          'hu': 'Magyar',
          'cs': 'Čeština',
          'uk': 'Українська',
          'ru': 'Русский',
          'bg': 'Български',
          'ar': 'العربية',
          'fa': 'فارسی',
          'he': 'עִבְרִית',
          'hi': 'हिन्दी',
          'th': 'ไทย',
          'ja': '日本語',
          'zh-CN': '中文（简体）',
          'zh-TW': '中文（繁體）',
          'ko': '한국어'
        };
        selectedLanguageEl.textContent = languageMap[target.value] || target.value;
      }
    });
  }

  // 表单提交
  const form = inPagePopup.querySelector('#fillify-form-config');
  const generateBtn = inPagePopup.querySelector('#fillify-fill-button');
  if (form && generateBtn) {
    const handleSubmit = async (e: Event) => {
      e.preventDefault();
      await handleGenerate();
    };

    form.addEventListener('submit', handleSubmit);
    generateBtn.addEventListener('click', handleSubmit);
  }

  // 更新模式选择器的滑动背景位置
  updateModeBackground();

  // 添加拖拽功能
  setupDragFunctionality();
}

// 设置拖拽功能
function setupDragFunctionality() {
  if (!inPagePopup) return;
  
  // 长按检测开始拖拽
  const handleMouseDown = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    
    // 排除交互元素
    if (target.tagName === 'BUTTON' || 
        target.tagName === 'INPUT' || 
        target.tagName === 'TEXTAREA' || 
        target.tagName === 'SELECT' ||
        target.closest('button, input, textarea, select, .fillify-menu-popover')) {
      return;
    }
    
    // 阻止默认行为，避免干扰其他事件
    e.preventDefault();
    
    // 记录初始位置
    dragStartX = e.clientX;
    dragStartY = e.clientY;
    
    const rect = inPagePopup!.getBoundingClientRect();
    popupStartX = rect.left;
    popupStartY = rect.top;
    
    isLongPressTriggered = false;
    
    // 启动长按计时器
    longPressTimer = window.setTimeout(() => {
      isLongPressTriggered = true;
      startDragging();
    }, 300); // 300ms长按检测
    
    // 监听鼠标移动和释放
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp, { passive: false });
  };
  
  const handleMouseMove = (e: MouseEvent) => {
    if (longPressTimer && !isLongPressTriggered) {
      // 如果在长按期间移动鼠标，取消长按
      const moveThreshold = 5;
      if (Math.abs(e.clientX - dragStartX) > moveThreshold || 
          Math.abs(e.clientY - dragStartY) > moveThreshold) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
      }
    }
    
    if (isDragging && inPagePopup) {
      e.preventDefault();
      e.stopPropagation();
      
      const deltaX = e.clientX - dragStartX;
      const deltaY = e.clientY - dragStartY;
      
      let newX = popupStartX + deltaX;
      let newY = popupStartY + deltaY;
      
      // 边界检查
      const rect = inPagePopup.getBoundingClientRect();
      const maxX = window.innerWidth - rect.width;
      const maxY = window.innerHeight - rect.height;
      
      newX = Math.max(0, Math.min(newX, maxX));
      newY = Math.max(0, Math.min(newY, maxY));
      
      inPagePopup.style.left = `${newX}px`;
      inPagePopup.style.top = `${newY}px`;
      inPagePopup.style.right = 'auto';
      inPagePopup.style.bottom = 'auto';
    }
  };
  
  const handleMouseUp = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      longPressTimer = null;
    }
    
    if (isDragging) {
      stopDragging();
    }
    
    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove, { passive: false } as any);
    document.removeEventListener('mouseup', handleMouseUp, { passive: false } as any);
  };
  
  const startDragging = () => {
    if (!inPagePopup) return;
    
    isDragging = true;
    inPagePopup.style.cursor = 'grabbing';
    inPagePopup.style.transform = 'scale(1.02)';
    inPagePopup.style.boxShadow = '0 25px 70px rgba(0, 0, 0, 0.4)';
    inPagePopup.style.transition = 'transform 0.1s ease, box-shadow 0.1s ease'; // 缩短到0.1s
    
    // 禁用文本选择
    document.body.style.userSelect = 'none';
  };
  
  const stopDragging = () => {
    if (!inPagePopup) return;
    
    isDragging = false;
    inPagePopup.style.cursor = '';
    inPagePopup.style.transform = '';
    inPagePopup.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.3)';
    inPagePopup.style.transition = 'transform 0.15s ease, box-shadow 0.15s ease'; // 缩短到0.15s
    
    // 恢复文本选择
    document.body.style.userSelect = '';
    
    // 更快地清理过渡效果
    setTimeout(() => {
      if (inPagePopup) {
        inPagePopup.style.transition = '';
      }
    }, 150); // 从200ms减少到150ms
  };
  
  // 绑定鼠标按下事件
  inPagePopup.addEventListener('mousedown', handleMouseDown);
}

// 更新模式选择器的滑动背景位置
function updateModeBackground() {
  if (!inPagePopup) return;
  
  const modeButtons = inPagePopup.querySelector('.fillify-mode-buttons') as HTMLElement;
  if (!modeButtons) return;

  // 获取当前激活的按钮
  const activeBtn = modeButtons.querySelector('.fillify-mode-btn.active') as HTMLElement;
  if (!activeBtn) return;

  const mode = activeBtn.getAttribute('data-mode');
  
  // 根据模式设置transform值
  let transformValue = 'translateX(0)';
  if (mode === 'email') {
    transformValue = 'translateX(calc(100% + 4px))';
  } else if (mode === 'bugReport') {
    transformValue = 'translateX(calc(200% + 8px))';
  }

  // 创建或更新样式
  let modeStyleTag = document.querySelector('#fillify-mode-style') as HTMLStyleElement;
  if (!modeStyleTag) {
    modeStyleTag = document.createElement('style');
    modeStyleTag.id = 'fillify-mode-style';
    document.head.appendChild(modeStyleTag);
  }
  
  modeStyleTag.textContent = `
    .fillify-mode-buttons::after {
      transform: ${transformValue} !important;
    }
  `;
}

// 处理生成请求
async function handleGenerate() {
  const generateBtn = inPagePopup?.querySelector('#fillify-fill-button') as HTMLButtonElement;
  const btnText = inPagePopup?.querySelector('.fillify-button-text') as HTMLElement;
  const sparkleIcon = inPagePopup?.querySelector('.fillify-sparkle-icon') as HTMLElement;
  const loadingAnimation = inPagePopup?.querySelector('.fillify-animation') as HTMLElement;
  const descriptionTextarea = inPagePopup?.querySelector('#fillify-description') as HTMLTextAreaElement;
  const languageSelect = inPagePopup?.querySelector('#fillify-language-select') as HTMLSelectElement;
  const projectSelect = inPagePopup?.querySelector('#fillify-project-select') as HTMLSelectElement;

  if (!generateBtn || !btnText || !sparkleIcon || !loadingAnimation || !descriptionTextarea) {
    Logger.error('Required elements not found in popup');
    return;
  }

  const description = descriptionTextarea.value.trim();
  if (!description) {
    showStatusMessage('Please enter a description', 'error');
    return;
  }

  try {
    // 显示加载状态
    generateBtn.disabled = true;
    generateBtn.classList.add('loading');
    btnText.textContent = 'Generating...';
    sparkleIcon.classList.add('hidden');
    loadingAnimation.style.display = 'block';

    // 获取当前模式
    const storage = await chrome.storage.sync.get(['formify_last_mode']);
    const currentMode = storage.formify_last_mode || 'general';

    // 1. 检测表单字段
    const formFields = detectFormFields();
    if (!formFields.length) {
      throw new Error('No form fields detected on this page');
    }

    // 2. 显示生成效果
    showGeneratingEffect();

    // 3. 发送AI请求
    const aiResponse = await chrome.runtime.sendMessage({
      type: 'aiRequest',
      prompt: description,
      options: {
        mode: currentMode,
        projectId: projectSelect?.value || '',
        language: languageSelect?.value || 'auto',
        description: description,
        formFields: formFields.map(field => ({
          type: field.type,
          id: field.id,
          name: field.name,
          placeholder: field.placeholder,
          label: field.label
        }))
      }
    });

    if (!aiResponse?.success) {
      throw new Error(aiResponse?.error || 'Failed to generate content');
    }

    // 4. 填充表单
    await new Promise<void>((resolve, reject) => {
      fillForm(aiResponse.data.data || aiResponse.data, {
        success: () => {
          Logger.success('fill', 'Form filled successfully');
          resolve();
        },
        error: (error) => {
          Logger.error('Failed to fill form:', error);
          reject(new Error(error instanceof Error ? error.message : String(error)));
        }
      });
    });

    // 5. 成功状态
    generateBtn.classList.remove('loading');
    generateBtn.classList.add('success');
    btnText.textContent = 'Finish';
    loadingAnimation.style.display = 'none';
    sparkleIcon.classList.remove('hidden');

    // 触发彩带动画
    createConfetti();

    showStatusMessage('Form filled successfully!', 'success');

    // 延迟关闭popup
    setTimeout(() => {
      hideInPagePopup();
    }, 1500);

  } catch (error) {
    Logger.error('Error in handleGenerate:', error);
    showStatusMessage(error instanceof Error ? error.message : 'An error occurred', 'error');
  } finally {
    // 恢复按钮状态（如果不是成功状态）
    if (!generateBtn.classList.contains('success')) {
      generateBtn.disabled = false;
      generateBtn.classList.remove('loading');
      btnText.textContent = 'Generate';
      sparkleIcon.classList.remove('hidden');
      loadingAnimation.style.display = 'none';
    }

    // 清除生成效果
    clearGeneratingEffect();
  }
}

// 显示状态消息
function showStatusMessage(message: string, type: 'info' | 'error' | 'success' = 'info') {
  // 先尝试使用popup内置的状态消息系统
  const statusContainer = inPagePopup?.querySelector('#fillify-status-container');
  const statusMessage = inPagePopup?.querySelector('#fillify-status-message');
  
  if (statusContainer && statusMessage) {
    // 使用popup内置的状态消息系统
    statusMessage.textContent = message;
    statusMessage.className = `fillify-status-message show ${type}`;
    
    // 自动隐藏
    setTimeout(() => {
      statusMessage.classList.remove('show');
    }, 3000);
  } else {
    // 降级到创建独立的消息元素
    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: ${type === 'error' ? 'rgba(244, 67, 54, 0.9)' :
                   type === 'success' ? 'rgba(76, 175, 80, 0.9)' :
                   'rgba(33, 150, 243, 0.9)'};
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 10002;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      opacity: 0;
      transition: opacity 0.3s ease;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    messageEl.textContent = message;

    document.body.appendChild(messageEl);

    // 显示动画
    setTimeout(() => {
      messageEl.style.opacity = '1';
    }, 10);

    // 自动移除
    setTimeout(() => {
      messageEl.style.opacity = '0';
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.parentNode.removeChild(messageEl);
        }
      }, 300);
    }, 3000);
  }
}

// 清除生成效果
function clearGeneratingEffect() {
  if (generatingStyleTag && generatingStyleTag.parentNode) {
    generatingStyleTag.parentNode.removeChild(generatingStyleTag);
    generatingStyleTag = null;
  }

  // 移除所有加载类
  const elementsWithLoadingClass = document.querySelectorAll('.fillify-generating');
  elementsWithLoadingClass.forEach(el => {
    el.classList.remove('fillify-generating');
  });
}

// Confetti animation system (matching Chrome popup)
function createConfetti() {
  const confettiCanvas = inPagePopup?.querySelector('#fillify-confetti-canvas') as HTMLCanvasElement;
  if (!confettiCanvas) return;

  const ctx = confettiCanvas.getContext('2d');
  if (!ctx) return;

  // 设置canvas尺寸
  const rect = inPagePopup!.getBoundingClientRect();
  confettiCanvas.width = rect.width;
  confettiCanvas.height = rect.height;

  // Confetti configuration matching Chrome popup
  const confettiConfig = {
    confettiCount: 20,
    sequinCount: 10,
    gravityConfetti: 0.3,
    gravitySequins: 0.55,
    dragConfetti: 0.075,
    dragSequins: 0.02,
    terminalVelocity: 3,
    colors: [
      { front: '#7b5cff', back: '#6245e0' }, // Purple
      { front: '#b3c7ff', back: '#8fa5e5' }, // Light blue
      { front: '#5c86ff', back: '#345dd1' }  // Dark blue
    ]
  };

  let confetti: ConfettoInstance[] = [];
  let sequins: SequinInstance[] = [];
  let animationFrame: number | null = null;

  // Helper function
  const randomRange = (min: number, max: number) => {
    return Math.random() * (max - min) + min;
  };

  const initConfettoVelocity = (xRange: [number, number], yRange: [number, number]) => {
    const x = randomRange(xRange[0], xRange[1]);
    const range = yRange[1] - yRange[0] + 1;
    let y = yRange[1] - Math.abs(randomRange(0, range) + randomRange(0, range) - range);
    if (y >= yRange[1] - 1) {
      y += Math.random() < 0.25 ? randomRange(1, 3) : 0;
    }
    return { x: x, y: -y };
  };

  // Confetto class
  interface ConfettoInstance {
    randomModifier: number;
    color: { front: string; back: string };
    dimensions: { x: number; y: number };
    position: { x: number; y: number };
    rotation: number;
    scale: { x: number; y: number };
    velocity: { x: number; y: number };
    update(): void;
  }

  class Confetto implements ConfettoInstance {
    randomModifier: number;
    color: { front: string; back: string };
    dimensions: { x: number; y: number };
    position: { x: number; y: number };
    rotation: number;
    scale: { x: number; y: number };
    velocity: { x: number; y: number };

    constructor() {
      const button = inPagePopup?.querySelector('#fillify-fill-button');
      const buttonRect = button?.getBoundingClientRect();
      const popupRect = inPagePopup?.getBoundingClientRect();
      
      const buttonCenterX = buttonRect ? 
        (buttonRect.left + buttonRect.width / 2) - (popupRect?.left || 0) : 
        confettiCanvas.width / 2;
      const buttonTop = buttonRect ? 
        (buttonRect.top) - (popupRect?.top || 0) : 
        confettiCanvas.height / 2;

      this.randomModifier = randomRange(0, 99);
      this.color = confettiConfig.colors[Math.floor(randomRange(0, confettiConfig.colors.length))];
      this.dimensions = { x: randomRange(5, 9), y: randomRange(8, 15) };
      this.position = {
        x: buttonCenterX + randomRange(-10, 10),
        y: buttonTop + randomRange(-5, 5)
      };
      this.rotation = randomRange(0, 2 * Math.PI);
      this.scale = { x: 1, y: 1 };
      this.velocity = initConfettoVelocity([-9, 9], [6, 11]);
    }

    update() {
      this.velocity.x -= this.velocity.x * confettiConfig.dragConfetti;
      this.velocity.y = Math.min(this.velocity.y + confettiConfig.gravityConfetti, confettiConfig.terminalVelocity);
      this.position.x += this.velocity.x;
      this.position.y += this.velocity.y;
      this.scale.y = Math.cos((this.position.y + this.randomModifier) * 0.09);
    }
  }

  // Sequin class
  interface SequinInstance {
    color: string;
    radius: number;
    position: { x: number; y: number };
    velocity: { x: number; y: number };
    update(): void;
  }

  class Sequin implements SequinInstance {
    color: string;
    radius: number;
    position: { x: number; y: number };
    velocity: { x: number; y: number };

    constructor() {
      const button = inPagePopup?.querySelector('#fillify-fill-button');
      const buttonRect = button?.getBoundingClientRect();
      const popupRect = inPagePopup?.getBoundingClientRect();
      
      const buttonCenterX = buttonRect ? 
        (buttonRect.left + buttonRect.width / 2) - (popupRect?.left || 0) : 
        confettiCanvas.width / 2;
      const buttonTop = buttonRect ? 
        (buttonRect.top) - (popupRect?.top || 0) : 
        confettiCanvas.height / 2;

      this.color = confettiConfig.colors[Math.floor(randomRange(0, confettiConfig.colors.length))].back;
      this.radius = randomRange(1, 2);
      this.position = {
        x: buttonCenterX + randomRange(-15, 15),
        y: buttonTop + randomRange(-5, 5)
      };
      this.velocity = {
        x: randomRange(-15, 15),
        y: randomRange(1, 5)
      };
    }

    update() {
      this.velocity.x -= this.velocity.x * confettiConfig.dragSequins;
      this.velocity.y = Math.min(this.velocity.y + confettiConfig.gravitySequins, confettiConfig.terminalVelocity);
      this.position.x += this.velocity.x;
      this.position.y += this.velocity.y;
    }
  }

  const initBurst = () => {
    for (let i = 0; i < confettiConfig.confettiCount; i++) {
      confetti.push(new Confetto());
    }
    for (let i = 0; i < confettiConfig.sequinCount; i++) {
      sequins.push(new Sequin());
    }
  };

  const render = () => {
    ctx.clearRect(0, 0, confettiCanvas.width, confettiCanvas.height);

    confetti.forEach((confetto, index) => {
      confetto.update();
      ctx.fillStyle = confetto.scale.y > 0 ? confetto.color.front : confetto.color.back;
      ctx.save();
      ctx.translate(confetto.position.x, confetto.position.y);
      ctx.rotate(confetto.rotation);
      ctx.fillRect(-confetto.dimensions.x / 2, -confetto.dimensions.y / 2, confetto.dimensions.x, confetto.dimensions.y);
      ctx.restore();
      if (confetto.position.y >= confettiCanvas.height + 100) confetti.splice(index, 1);
    });

    sequins.forEach((sequin, index) => {
      sequin.update();
      ctx.fillStyle = sequin.color;
      ctx.beginPath();
      ctx.arc(sequin.position.x, sequin.position.y, sequin.radius, 0, 2 * Math.PI);
      ctx.fill();
      if (sequin.position.y >= confettiCanvas.height + 100) sequins.splice(index, 1);
    });

    if (confetti.length === 0 && sequins.length === 0) {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
        animationFrame = null;
      }
      ctx.clearRect(0, 0, confettiCanvas.width, confettiCanvas.height);
      return;
    }

    animationFrame = requestAnimationFrame(render);
  };

  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
  }
  initBurst();
  render();
}
