{"name": "wxt-vue-starter", "description": "manifest.json description", "private": true, "version": "1.0.6", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare", "clean": "rimraf .output", "lint": "eslint . --ext .ts,.tsx,.vue,.js --fix", "lint:check": "eslint . --ext .ts,.tsx,.vue,.js", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit", "pre-commit": "npm run lint:check && npm run format:check && npm run type-check"}, "dependencies": {"ai": "^4.3.16", "canvas-confetti": "^1.9.3", "vue": "^3.5.12"}, "devDependencies": {"@types/chrome": "^0.0.280", "@wxt-dev/module-vue": "^1.0.1", "typescript": "5.6.3", "vue-tsc": "^2.1.10", "wxt": "^0.19.28", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@eslint/js": "^8.57.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-security": "^1.7.1", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.5", "rimraf": "^5.0.5"}}